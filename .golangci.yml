version: "2"
linters:
  default: none
  enable:
    - asciicheck
    - bodyclose
    - copyloopvar
    - decorder
    - dogsled
    - errname
    - errorlint
    - funlen
    - gocheckcompilerdirectives
    - goconst
    - gocyclo
    - godox
    - goprintffuncname
    - gosec
    - ineffassign
    - misspell
    - mnd
    - nakedret
    - nlreturn
    - nolintlint
    - perfsprint
    - prealloc
    - revive
    - unconvert
    - unparam
    - unused
    - whitespace
  settings:
    forbidigo:
      forbid:
        - pattern: ^print.*$
        - pattern: ^fmt\.Print.*$
          msg: Do not commit print statements.
        - pattern: fmt\.Print.*(# Do not commit print statements\.)?
    funlen:
      lines: -1
      statements: 100
      ignore-comments: true
    gocyclo:
      min-complexity: 50
    misspell:
      locale: US
    mnd:
      checks:
        - argument
        - case
        - condition
        - return
      ignored-numbers:
        - "0"
        - "1"
        - "2"
        - "3"
      ignored-functions:
        - strings.SplitN
    nolintlint:
      require-explanation: false
      require-specific: false
      allow-unused: false
    revive:
      rules:
        - name: unexported-return
          disabled: true
        - name: unused-parameter
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - gocritic
          - gocyclo
          - gosec
        path: _test\.go
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  max-same-issues: 20
formatters:
  enable:
    - gci
    - gofmt
  settings:
    gofmt:
      rewrite-rules:
        - pattern: interface{}
          replacement: any
        - pattern: a[b:len(a)]
          replacement: a[b:]
    goimports:
      local-prefixes:
        - inv-cloud-platform
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
