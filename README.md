# EV Receipt Template Backend-For-Frontend (BFF) Service

This service provides a backend-for-frontend API for managing electronic vehicle (EV) receipt templates. It follows a hexagonal architecture pattern and serves as an intermediary between frontend applications and the template storage service.

## Purpose

The EV Receipt Template BFF service allows clients to:
- Retrieve receipt templates by template ID and connection ID
- Create new receipt templates
- Update existing receipt templates
- Delete receipt templates

Each template contains customizable elements including:
- Header and footer text with font, style, and color properties
- Header and footer icons with dimensions
- Template body content

## Architecture

This service follows the Hexagonal Architecture (Ports and Adapters) pattern:

- **Core Domain**: Contains the business entities and logic
  - `domain`: Template data structures and business rules
  - `port`: Interface definitions for the application's input and output ports
  - `service`: Implementation of business logic
  - `usecase`: Application use cases

- **Adapters**: Connect the core to external systems
  - `httpapi`: HTTP client for communicating with the template storage service
  - `resthttp`: HTTP server for exposing the API to clients

## API Documentation

The API is documented using OpenAPI 3.0 specification. You can access the API documentation through:

- `/swagger` - Swagger UI
- `/scalar` - Scalar UI
- `/redoc` - ReDoc UI

### Endpoints

- `GET /api/v1/templates/{connection_id}/{template_id}` - Retrieve a template
- `POST /api/v1/templates/{connection_id}` - Create a new template
- `PUT /api/v1/templates/{connection_id}/{template_id}` - Update an existing template
- `DELETE /api/v1/templates/{connection_id}/{template_id}` - Delete a template

## Setup and Development

### Prerequisites

- Go v1.23 or higher
- Docker
- Make

### Getting Started

1. Clone the repository
2. Install dependencies:
   ```
   go mod download
   go mod tidy
   ```

3. Run the service locally:
   ```
   make run
   ```

4. Run tests:
   ```
   make test
   ```

### Docker

Build and run the service using Docker:

```
make docker-build
make docker-run
```

For development with Docker Compose:

```
cd example
docker-compose up
```

## Deployment

To deploy the app, use the [deployment repository](https://github.com/inv-cloud-platform/{subsystem}-deploy) by following the steps in its README.md.

To create and push a Docker image to the image repository:

```
git tag release/v0.0.1 && git push origin release/v0.0.1
```

## Configuration

Configuration is loaded from environment variables. See `config/config.go` for available options.

## Testing

The project includes:
- Unit tests
- Integration tests using testcontainers
- Mock API for testing

Run tests with:
```
make test
```

## Static Analysis

The project uses [golangci-lint](https://golangci-lint.run/usage/quick-start/) for static code analysis:

```
make lint
```

## Contributing

Follow the project's coding standards and submit pull requests for review.

## License

Proprietary - Invenco by GVR