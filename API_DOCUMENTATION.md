# EV Receipt Template API Documentation

## Overview

This API provides endpoints for managing electronic vehicle (EV) receipt templates. It follows a RESTful design and serves as a Backend-For-Frontend (BFF) service that acts as an intermediary between frontend applications and the template storage service.

## Base URL

```
https://api.example.com
```

## Authentication

All API endpoints require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## API Endpoints

### 1. List All Templates

Retrieves a list of all available receipt templates.

**Request**

```http
GET /api/v1/templates
```

**Response**

Status Code: 200 OK

```json
[
  {
    "template_id": "a7e3199b-4df4-4975-bfe9-d1b4d22a7d99",
    "connection_id": "550e8400-e29b-41d4-a716-************",
    "is_default": true,
    "header": {
      "header_text": {
        "font": "Arial",
        "style": "Bold",
        "color": "#000000",
        "value": "Receipt Header"
      },
      "header_icon": {
        "src": "https://example.com/images/logo.png",
        "width": 100,
        "height": 50
      }
    },
    "body": "Thank you for charging with us!\n\nCharging Session Details:\n{{session_details}}\n\nPayment Information:\n{{payment_info}}",
    "footer": {
      "footer_text": {
        "font": "Arial",
        "style": "Italic",
        "color": "#333333",
        "value": "Thank you for your business"
      },
      "footer_icon": {
        "src": "https://example.com/images/footer-logo.png",
        "width": 80,
        "height": 40
      }
    },
    "connection": {
      "connection_name": "Example Connection",
      "country_code": "US",
      "party_code": "EXP",
      "cpo_url": "https://example.com/cpo"
    },
    "last_updated": "2023-10-29T12:00:00Z"
  },
  {
    "template_id": "b8f4200c-5ef5-5086-cga0-e2c5e33b8e00",
    "connection_id": "661f9511-f30c-52e5-b827-557766551111",
    "is_default": false,
    "header": {
      "header_text": {
        "font": "Helvetica",
        "style": "Normal",
        "color": "#222222",
        "value": "Receipt"
      },
      "header_icon": {
        "src": "https://example.com/images/alt-logo.png",
        "width": 120,
        "height": 60
      }
    },
    "body": "Thank you for using our charging station!\n\nSession Summary:\n{{session_summary}}\n\nBilling Details:\n{{billing_details}}",
    "footer": {
      "footer_text": {
        "font": "Helvetica",
        "style": "Normal",
        "color": "#444444",
        "value": "Visit again"
      },
      "footer_icon": {
        "src": "https://example.com/images/alt-footer.png",
        "width": 90,
        "height": 45
      }
    },
    "connection": {
      "connection_name": "Alternative Connection",
      "country_code": "UK",
      "party_code": "ALT",
      "cpo_url": "https://alt-example.com/cpo"
    },
    "last_updated": "2023-10-30T14:30:00Z"
  }
]
```

**Error Responses**

| Status Code | Description |
|-------------|-------------|
| 401 | Unauthorized - Authentication failed or token is invalid |
| 500 | Internal Server Error |

### 2. Get Template by ID

Retrieves a specific receipt template by its template ID and connection ID.

**Request**

```http
GET /api/v1/templates/{connection_id}/{template_id}
```

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| connection_id | string | Unique connection ID (UUID format, 36 characters) |
| template_id | string | Unique template ID (UUID format, 36 characters) |

**Response**

Status Code: 200 OK

```json
{
  "template_id": "a7e3199b-4df4-4975-bfe9-d1b4d22a7d99",
  "connection_id": "550e8400-e29b-41d4-a716-************",
  "is_default": true,
  "header": {
    "header_text": {
      "font": "Arial",
      "style": "Bold",
      "color": "#000000",
      "value": "Receipt Header"
    },
    "header_icon": {
      "src": "https://example.com/images/logo.png",
      "width": 100,
      "height": 50
    }
  },
  "body": "Thank you for charging with us!\n\nCharging Session Details:\n{{session_details}}\n\nPayment Information:\n{{payment_info}}",
  "footer": {
    "footer_text": {
      "font": "Arial",
      "style": "Italic",
      "color": "#333333",
      "value": "Thank you for your business"
    },
    "footer_icon": {
      "src": "https://example.com/images/footer-logo.png",
      "width": 80,
      "height": 40
    }
  },
  "connection": {
    "connection_name": "Example Connection",
    "country_code": "US",
    "party_code": "EXP",
    "cpo_url": "https://example.com/cpo"
  },
  "last_updated": "2023-10-29T12:00:00Z"
}
```

**Error Responses**

| Status Code | Description |
|-------------|-------------|
| 401 | Unauthorized - Authentication failed or token is invalid |
| 404 | Not Found - Template not found |
| 500 | Internal Server Error |

### 3. Create New Template

Creates a new receipt template for a specific connection.

**Request**

```http
POST /api/v1/templates/{connection_id}
```

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| connection_id | string | Unique connection ID (UUID format, 36 characters) |

**Request Body**

```json
{
  "is_default": true,
  "header": {
    "header_text": {
      "font": "Arial",
      "style": "Bold",
      "color": "#000000",
      "value": "Receipt Header"
    },
    "header_icon": {
      "src": "https://example.com/images/logo.png",
      "width": 100,
      "height": 50
    }
  },
  "body": "Thank you for charging with us!\n\nCharging Session Details:\n{{session_details}}\n\nPayment Information:\n{{payment_info}}",
  "footer": {
    "footer_text": {
      "font": "Arial",
      "style": "Italic",
      "color": "#333333",
      "value": "Thank you for your business"
    },
    "footer_icon": {
      "src": "https://example.com/images/footer-logo.png",
      "width": 80,
      "height": 40
    }
  },
  "connection": {
    "connection_name": "Example Connection",
    "country_code": "US",
    "party_code": "EXP",
    "cpo_url": "https://example.com/cpo"
  }
}
```

**Response**

Status Code: 201 Created

**Error Responses**

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Authentication failed or token is invalid |
| 409 | Conflict - Template already exists with same information |
| 500 | Internal Server Error |

### 4. Update Template

Updates an existing receipt template.

**Request**

```http
PUT /api/v1/templates/{connection_id}/{template_id}
```

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| connection_id | string | Unique connection ID (UUID format, 36 characters) |
| template_id | string | Unique template ID (UUID format, 36 characters) |

**Request Body**

```json
{
  "is_default": true,
  "header": {
    "header_text": {
      "font": "Arial",
      "style": "Bold",
      "color": "#000000",
      "value": "Updated Receipt Header"
    },
    "header_icon": {
      "src": "https://example.com/images/updated-logo.png",
      "width": 120,
      "height": 60
    }
  },
  "body": "Thank you for charging with us!\n\nUpdated charging session details:\n{{session_details}}\n\nPayment Information:\n{{payment_info}}",
  "footer": {
    "footer_text": {
      "font": "Arial",
      "style": "Italic",
      "color": "#333333",
      "value": "Thank you for your continued business"
    },
    "footer_icon": {
      "src": "https://example.com/images/updated-footer-logo.png",
      "width": 90,
      "height": 45
    }
  },
  "connection": {
    "connection_name": "Updated Connection",
    "country_code": "US",
    "party_code": "EXP",
    "cpo_url": "https://example.com/updated-cpo"
  }
}
```

**Response**

Status Code: 204 No Content

**Error Responses**

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Authentication failed or token is invalid |
| 404 | Not Found - Template not found |
| 409 | Conflict - Template already exists with same information |
| 500 | Internal Server Error |

### 5. Delete Template

Deletes an existing receipt template.

**Request**

```http
DELETE /api/v1/templates/{connection_id}/{template_id}
```

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| connection_id | string | Unique connection ID (UUID format, 36 characters) |
| template_id | string | Unique template ID (UUID format, 36 characters) |

**Response**

Status Code: 204 No Content

**Error Responses**

| Status Code | Description |
|-------------|-------------|
| 401 | Unauthorized - Authentication failed or token is invalid |
| 404 | Not Found - Template not found |
| 500 | Internal Server Error |

## Data Models

### Template Object

| Field | Type | Description |
|-------|------|-------------|
| template_id | string | Unique identifier for the template (UUID format) |
| connection_id | string | Unique identifier for the connection (UUID format) |
| is_default | boolean | Indicates if this is the default template for the connection |
| header | object | Contains header text and icon information |
| body | string | The main content of the receipt template with placeholders |
| footer | object | Contains footer text and icon information |
| connection | object | Contains connection details |
| last_updated | string | ISO 8601 timestamp of when the template was last updated |

### Text Object

| Field | Type | Description |
|-------|------|-------------|
| font | string | Font family name |
| style | string | Text style (Bold, Italic, Normal, etc.) |
| color | string | Text color in hexadecimal format |
| value | string | The actual text content |

### Icon Object

| Field | Type | Description |
|-------|------|-------------|
| src | string | URL to the icon image |
| width | integer | Width of the icon in pixels |
| height | integer | Height of the icon in pixels |

### Connection Object

| Field | Type | Description |
|-------|------|-------------|
| connection_name | string | Name of the connection |
| country_code | string | ISO country code |
| party_code | string | Unique code for the party |
| cpo_url | string | URL of the Charge Point Operator |

## Error Response Object

| Field | Type | Description |
|-------|------|-------------|
| status_code | integer | HTTP status code |
| status_message | string | Human-readable error message |
| timestamp | string | ISO 8601 timestamp of when the error occurred |
