//go:build go1.22

// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
)

const (
	BearerAuthScopes = "BearerAuth.Scopes"
)

// Body defines model for Body.
type Body = string

// Connection defines model for Connection.
type Connection struct {
	ConnectionName string `json:"connection_name,omitempty"`
	CountryCode    string `json:"country_code,omitempty"`
	CpoUrl         string `json:"cpo_url,omitempty"`
	PartyCode      string `json:"party_code,omitempty"`
}

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	StatusCode *int `json:"status_code,omitempty"`

	// StatusMessage A description of the error.
	StatusMessage *string `json:"status_message,omitempty"`

	// Timestamp The time this message was generated.
	Timestamp *time.Time `json:"timestamp,omitempty"`
}

// Footer defines model for Footer.
type Footer struct {
	FooterIcon *Icon `json:"footer_icon,omitempty"`
	FooterText *Text `json:"footer_text,omitempty"`
}

// Header defines model for Header.
type Header struct {
	HeaderIcon *Icon `json:"header_icon,omitempty"`
	HeaderText *Text `json:"header_text,omitempty"`
}

// Icon defines model for Icon.
type Icon struct {
	Height *int    `json:"height,omitempty"`
	Name   *string `json:"name,omitempty"`
	Src    *string `json:"src,omitempty"`
	Width  *int    `json:"width,omitempty"`
}

// TemplateResponse defines model for TemplateResponse.
type TemplateResponse struct {
	Body         *Body       `json:"body,omitempty"`
	Connection   *Connection `json:"connection,omitempty"`
	ConnectionId *string     `json:"connection_id,omitempty"`
	Footer       *Footer     `json:"footer,omitempty"`
	Header       *Header     `json:"header,omitempty"`
	IsDefault    *bool       `json:"is_default,omitempty"`
	LastUpdated  *time.Time  `json:"last_updated,omitempty"`
	TemplateId   *string     `json:"template_id,omitempty"`
}

// TemplatesResponse defines model for TemplatesResponse.
type TemplatesResponse = []TemplateResponse

// Text defines model for Text.
type Text struct {
	Color *string `json:"color,omitempty"`
	Font  *string `json:"font,omitempty"`
	Style *string `json:"style,omitempty"`
	Value *string `json:"value,omitempty"`
}

// ConnectionID Unique connection id of party for which service is requesting receipt template.
type ConnectionID = string

// TemplateID Unique id of the new or updated receipt template object.
type TemplateID = string

// NewTemplateJSONRequestBody defines body for NewTemplate for application/json ContentType.
type NewTemplateJSONRequestBody = TemplateResponse

// UpdateTemplateJSONRequestBody defines body for UpdateTemplate for application/json ContentType.
type UpdateTemplateJSONRequestBody = TemplateResponse

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Retrieve All Receipts Template Information
	// (GET /api/v1/templates)
	GetTemplates(w http.ResponseWriter, r *http.Request)
	// Add new template
	// (POST /api/v1/templates/{connection_id})
	NewTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID)
	// Delete existing template
	// (DELETE /api/v1/templates/{connection_id}/{template_id})
	DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
	// Retrieve Receipt Template Information
	// (GET /api/v1/templates/{connection_id}/{template_id})
	GetTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
	// Update existing template
	// (PUT /api/v1/templates/{connection_id}/{template_id})
	UpdateTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetTemplates operation middleware
func (siw *ServerInterfaceWrapper) GetTemplates(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTemplates(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// NewTemplate operation middleware
func (siw *ServerInterfaceWrapper) NewTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", r.PathValue("connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.NewTemplate(w, r, connectionId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteTemplate operation middleware
func (siw *ServerInterfaceWrapper) DeleteTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", r.PathValue("connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", r.PathValue("template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTemplate operation middleware
func (siw *ServerInterfaceWrapper) GetTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", r.PathValue("connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", r.PathValue("template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UpdateTemplate operation middleware
func (siw *ServerInterfaceWrapper) UpdateTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", r.PathValue("connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", r.PathValue("template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, StdHTTPServerOptions{})
}

// ServeMux is an abstraction of http.ServeMux.
type ServeMux interface {
	HandleFunc(pattern string, handler func(http.ResponseWriter, *http.Request))
	ServeHTTP(w http.ResponseWriter, r *http.Request)
}

type StdHTTPServerOptions struct {
	BaseURL          string
	BaseRouter       ServeMux
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, m ServeMux) http.Handler {
	return HandlerWithOptions(si, StdHTTPServerOptions{
		BaseRouter: m,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, m ServeMux, baseURL string) http.Handler {
	return HandlerWithOptions(si, StdHTTPServerOptions{
		BaseURL:    baseURL,
		BaseRouter: m,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options StdHTTPServerOptions) http.Handler {
	m := options.BaseRouter

	if m == nil {
		m = http.NewServeMux()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	m.HandleFunc("GET "+options.BaseURL+"/api/v1/templates", wrapper.GetTemplates)
	m.HandleFunc("POST "+options.BaseURL+"/api/v1/templates/{connection_id}", wrapper.NewTemplate)
	m.HandleFunc("DELETE "+options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.DeleteTemplate)
	m.HandleFunc("GET "+options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.GetTemplate)
	m.HandleFunc("PUT "+options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.UpdateTemplate)

	return m
}

type GetTemplatesRequestObject struct {
}

type GetTemplatesResponseObject interface {
	VisitGetTemplatesResponse(w http.ResponseWriter) error
}

type GetTemplates200JSONResponse TemplatesResponse

func (response GetTemplates200JSONResponse) VisitGetTemplatesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplatesdefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response GetTemplatesdefaultJSONResponse) VisitGetTemplatesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type NewTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	Body         *NewTemplateJSONRequestBody
}

type NewTemplateResponseObject interface {
	VisitNewTemplateResponse(w http.ResponseWriter) error
}

type NewTemplate201Response struct {
}

func (response NewTemplate201Response) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(201)
	return nil
}

type NewTemplate400Response struct {
}

func (response NewTemplate400Response) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(400)
	return nil
}

type NewTemplate401JSONResponse ErrorResponse

func (response NewTemplate401JSONResponse) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type NewTemplate409JSONResponse ErrorResponse

func (response NewTemplate409JSONResponse) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(409)

	return json.NewEncoder(w).Encode(response)
}

type NewTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response NewTemplatedefaultJSONResponse) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type DeleteTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
}

type DeleteTemplateResponseObject interface {
	VisitDeleteTemplateResponse(w http.ResponseWriter) error
}

type DeleteTemplate204Response struct {
}

func (response DeleteTemplate204Response) VisitDeleteTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteTemplate401JSONResponse ErrorResponse

func (response DeleteTemplate401JSONResponse) VisitDeleteTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type DeleteTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response DeleteTemplatedefaultJSONResponse) VisitDeleteTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
}

type GetTemplateResponseObject interface {
	VisitGetTemplateResponse(w http.ResponseWriter) error
}

type GetTemplate200JSONResponse TemplateResponse

func (response GetTemplate200JSONResponse) VisitGetTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplate401JSONResponse ErrorResponse

func (response GetTemplate401JSONResponse) VisitGetTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response GetTemplatedefaultJSONResponse) VisitGetTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type UpdateTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
	Body         *UpdateTemplateJSONRequestBody
}

type UpdateTemplateResponseObject interface {
	VisitUpdateTemplateResponse(w http.ResponseWriter) error
}

type UpdateTemplate204Response struct {
}

func (response UpdateTemplate204Response) VisitUpdateTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type UpdateTemplate400Response struct {
}

func (response UpdateTemplate400Response) VisitUpdateTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(400)
	return nil
}

type UpdateTemplate401JSONResponse ErrorResponse

func (response UpdateTemplate401JSONResponse) VisitUpdateTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(401)

	return json.NewEncoder(w).Encode(response)
}

type UpdateTemplate409Response struct {
}

func (response UpdateTemplate409Response) VisitUpdateTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(409)
	return nil
}

type UpdateTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response UpdateTemplatedefaultJSONResponse) VisitUpdateTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {
	// Retrieve All Receipts Template Information
	// (GET /api/v1/templates)
	GetTemplates(ctx context.Context, request GetTemplatesRequestObject) (GetTemplatesResponseObject, error)
	// Add new template
	// (POST /api/v1/templates/{connection_id})
	NewTemplate(ctx context.Context, request NewTemplateRequestObject) (NewTemplateResponseObject, error)
	// Delete existing template
	// (DELETE /api/v1/templates/{connection_id}/{template_id})
	DeleteTemplate(ctx context.Context, request DeleteTemplateRequestObject) (DeleteTemplateResponseObject, error)
	// Retrieve Receipt Template Information
	// (GET /api/v1/templates/{connection_id}/{template_id})
	GetTemplate(ctx context.Context, request GetTemplateRequestObject) (GetTemplateResponseObject, error)
	// Update existing template
	// (PUT /api/v1/templates/{connection_id}/{template_id})
	UpdateTemplate(ctx context.Context, request UpdateTemplateRequestObject) (UpdateTemplateResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// GetTemplates operation middleware
func (sh *strictHandler) GetTemplates(w http.ResponseWriter, r *http.Request) {
	var request GetTemplatesRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTemplates(ctx, request.(GetTemplatesRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTemplates")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTemplatesResponseObject); ok {
		if err := validResponse.VisitGetTemplatesResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// NewTemplate operation middleware
func (sh *strictHandler) NewTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID) {
	var request NewTemplateRequestObject

	request.ConnectionId = connectionId

	var body NewTemplateJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.NewTemplate(ctx, request.(NewTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "NewTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(NewTemplateResponseObject); ok {
		if err := validResponse.VisitNewTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteTemplate operation middleware
func (sh *strictHandler) DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request DeleteTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteTemplate(ctx, request.(DeleteTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteTemplateResponseObject); ok {
		if err := validResponse.VisitDeleteTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetTemplate operation middleware
func (sh *strictHandler) GetTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request GetTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTemplate(ctx, request.(GetTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTemplateResponseObject); ok {
		if err := validResponse.VisitGetTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// UpdateTemplate operation middleware
func (sh *strictHandler) UpdateTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request UpdateTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	var body UpdateTemplateJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.UpdateTemplate(ctx, request.(UpdateTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "UpdateTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(UpdateTemplateResponseObject); ok {
		if err := validResponse.VisitUpdateTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xYbW/bNhD+KwS3j5LtpMWA6lv6ki1DNxSZswFrg4CmThZbilTJkx0v0H8fSEq2ZClx",
	"02Qtuu1TIvnudC/Pc3ykG8p1UWoFCi1NbmjJDCsAwfirF1op4Ci0OnvproWiCS0Z5jSiihVAE8q3Jlci",
	"pRE18LESBlKaoKkgopbnUDDnnILlRpTOlCb0QomPFZCdOxEp0RkpmcENybQh61zwnFgwK8GBCEtcaLAo",
	"1JIY4CBKJAhFKRnChEa0YNevQS0xp8mTHyJaCNW9xE3p0rVohFrSuo7ovPG9tbQ2+GcVFqrBHIiCNdGG",
	"VGXKENJB6kQv3gPH+1dQtzn4UT3X6cb93bOKOjP08zW6BIMCvFNneKHmff+IXsdLHbubsf0gylj7MpmM",
	"Sy0UggndqCPKdaXQbK64Th8Wp9RXlZEPCeEh9MBE6m2/w3hcJ18Zo8052FIrC8NmWmRY2f3nuqBLMM6/",
	"MSjAWraEIXJOSOe6RQ+4hzpwwDUrSulCnihSKbgugTs8eQOiOa+MgdRZDiCAogCLrCiHz5znQNzPBHNh",
	"SZMaWTNLlqDAOMT2H348O34SH83i42fzo+NkNktmsz9pRDNtCoY0oQ7ksYtIxzg36Omp1r7p+83M/P0r",
	"wQNsvzeQ0YR+N91tq2kD/umZs6mj1gXhGg+5zJ3NaD4/AUvH8sn9/Xvl07g8LJ8zPsbbHMQyx3GUjRPZ",
	"wc/w0ftrkboVM4w1lk+7NG+nwaJZRHfV65eVXxrd3XSXR2eL9fzcbk5u7rc5W6gcemaDze0oD9k32Kkj",
	"KuxVChmrJAbGNf9mTFrYprPQWgLz9Uhm8ao5IAL6D7Mp6p1P9+3BXbO13eEKhMIexu8eKnbhmTFsE6IH",
	"HuyfQFKbUVhmWuE4jnEjxxG+YrIa+2VYrAsDvDICN7+5GkIyz4EZMCdVoMPCX522o/j5jzltTlw/Pf/r",
	"biw5YhkOZaEy7ZMQ6Dfm6av41e/x+Ys383j+y5vX8fPTUxrRFRgbVvDRZDaZufR1CYqVgib0yWQ2OaaR",
	"1yM+sykrxXR1NG1H7m8uAccWurAEVOqPM2IAjYAVWJICMiEhJS49V5I7Y9hCV0iYlANVYglbMSHZQsLk",
	"nfJBu45CcVmlYInUSx2RwI+IBF4RQD55p6ivyHiHs5Qm9EfALcS8oApg8bUcz2aNIEEIc2dlKQX33tP3",
	"NmyIneb6FDTucOzn0u/TVn51y8oAeQ4pYcrpNKyMIrbiHKzNKik9jDu8fpRk+6JiJFFvQNpmOcGMTCgn",
	"gp08CJqCONHhsw5qoJUYHudVUTCzoQk9b8BATqQk52HglrTdIme7RjhYs6WlyVvKpIzbXsWdXtFLF3yA",
	"y+lNbzfXnvHaHgQqN+ByYF4tb2fjKrJsBUQgEQo1SRmyBbMwgq5fYd2W4pmze495Oz6Bncm0955TXwax",
	"DxZbUf2osOwOux7Q4GjYqV5LQqPSAS6fBgL1PRcsbd+ags3Rl8PthWIV5tqIvyAlMXFbFRQ2DyNZWEba",
	"ENQfQBG/X1ZMijQk+uzLJfpCq0wKjiTecYFJAyzdELgWFi1ZC8yJZUVvW3zj2+AkTXtk63BewXrL+U/k",
	"+fSmo0bqgEQJCId4vxZSkmAamu1LaUKNsPylN/1sonfe+evoc9ZCj6tPh9UNamiKGyPsN0LGbxjjL28B",
	"VgfrYTxduEePJ6yILYGLTPDhV5/FhizFCtTujgi6o/dFzEkveFTl9ZUpM/tHj9PHEnn/c/MLqtFGiR4S",
	"oreI0IiW1UG2hlfr4R7YY9bdAvPCB/l6PPpaivSTTrn26/Z/QZb2A/JWPuKoePxXaseLW/jUoWsARE9G",
	"dj66eMJ0P7e8vawv678DAAD//wJbIGmNGgAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
