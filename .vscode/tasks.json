{"version": "2.0.0", "tasks": [{"label": "run-docker-compose", "type": "shell", "command": "docker-compose", "args": ["-f", "example/docker-compose.yaml", "up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Start Docker Compose services for development"}, {"label": "stop-docker-compose", "type": "shell", "command": "docker-compose", "args": ["-f", "example/docker-compose.yaml", "down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Stop Docker Compose services"}, {"label": "build", "type": "shell", "command": "go", "args": ["build", "-v", "./..."], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$go"], "detail": "Build the Go application"}, {"label": "test", "type": "shell", "command": "go", "args": ["test", "-v", "./..."], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$go"], "detail": "Run all tests"}, {"label": "test-integration", "type": "shell", "command": "go", "args": ["test", "-v", "./tests/integration/..."], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$go"], "dependsOn": "run-docker-compose", "detail": "Run integration tests with Docker dependencies"}, {"label": "clean", "type": "shell", "command": "go", "args": ["clean", "-cache"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Clean Go build cache"}, {"label": "mod-tidy", "type": "shell", "command": "go", "args": ["mod", "tidy"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Tidy Go modules"}]}