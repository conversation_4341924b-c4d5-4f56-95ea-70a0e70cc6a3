{"go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "go.formatTool": "goimports", "go.lintTool": "golangci-lint", "go.lintOnSave": "package", "go.vetOnSave": "package", "go.buildOnSave": "package", "go.testOnSave": false, "go.coverOnSave": false, "go.coverOnSingleTest": true, "go.coverageDecorator": {"type": "gutter", "coveredHighlightColor": "rgba(64,128,128,0.5)", "uncoveredHighlightColor": "rgba(128,64,64,0.25)", "coveredGutterStyle": "blockblue", "uncoveredGutterStyle": "blockred"}, "go.testFlags": ["-v", "-race"], "go.buildFlags": ["-v"], "go.testTimeout": "30s", "go.delveConfig": {"dlvLoadConfig": {"followPointers": true, "maxVariableRecurse": 1, "maxStringLen": 64, "maxArrayValues": 64, "maxStructFields": -1}, "apiVersion": 2, "showGlobalVariables": false, "debugAdapter": "legacy"}, "go.alternateTools": {"dlv": "dlv"}, "files.exclude": {"**/vendor": true, "**/.git": true, "**/.DS_Store": true, "**/node_modules": true}, "search.exclude": {"**/vendor": true, "**/node_modules": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "[go]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.snippetSuggestions": "none", "editor.defaultFormatter": "golang.go"}, "[go.mod]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}}