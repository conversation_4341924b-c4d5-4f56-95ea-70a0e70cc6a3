{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Main Application",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/main.go",
            "env": {
                "GO_ENV": "development"
            },
            "args": [],
            "showLog": true,
            "preLaunchTask": "run-docker-compose"
        },
        {
            "name": "Launch Main Application (No Docker)",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/main.go",
            "env": {
                "GO_ENV": "development"
            },
            "args": [],
            "showLog": true
        },
        {
            "name": "Debug Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${workspaceFolder}",
            "env": {
                "GO_ENV": "test"
            },
            "args": [
                "-test.v"
            ],
            "showLog": true
        },
        {
            "name": "Debug Current Test File",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${fileDirname}",
            "env": {
                "GO_ENV": "test"
            },
            "args": [
                "-test.v"
            ],
            "showLog": true
        },
        {
            "name": "Debug Integration Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${workspaceFolder}/tests/integration",
            "env": {
                "GO_ENV": "test"
            },
            "args": [
                "-test.v"
            ],
            "showLog": true,
            "preLaunchTask": "run-docker-compose"
        },
        {
            "name": "Attach to Process",
            "type": "go",
            "request": "attach",
            "mode": "local",
            "processId": "${command:pickProcess}"
        }
    ]
}