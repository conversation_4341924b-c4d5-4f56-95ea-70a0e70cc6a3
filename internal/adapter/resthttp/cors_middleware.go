package resthttp

import (
	"net/http"
)

// CORSMiddleware adds CORS headers to allow requests from all origins
func CORSMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.<PERSON>er().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Max-Age", "3600")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)

			return
		}

		// Call the next handler
		next.ServeHTTP(w, r)
	})
}
