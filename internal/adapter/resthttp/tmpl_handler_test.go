package resthttp

import (
	"context"
	"testing"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
)

// MockTemplateService implements the methods used by Handler
type MockTemplateService struct {
	RetrieveTemplateFunc func(ctx context.Context, templateID, connectionID string) (*domain.Template, error)
	NewTemplateFunc      func(ctx context.Context, template *domain.Template) error
	RemoveTemplateFunc   func(ctx context.Context, templateID, connectionID string) error
	UpdateTemplateFunc   func(ctx context.Context, template *domain.Template) error
	ListTemplateFunc     func(ctx context.Context) (domain.Templates, error)
}

func (m *MockTemplateService) RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	return m.RetrieveTemplateFunc(ctx, templateID, connectionID)
}

func (m *MockTemplateService) NewTemplate(ctx context.Context, template *domain.Template) error {
	if m.NewTemplateFunc != nil {
		return m.NewTemplateFunc(ctx, template)
	}

	return nil
}

func (m *MockTemplateService) RemoveTemplate(ctx context.Context, templateID, connectionID string) error {
	if m.RemoveTemplateFunc != nil {
		return m.RemoveTemplateFunc(ctx, templateID, connectionID)
	}

	return nil
}

func (m *MockTemplateService) UpdateTemplate(ctx context.Context, template *domain.Template) error {
	if m.UpdateTemplateFunc != nil {
		return m.UpdateTemplateFunc(ctx, template)
	}

	return nil
}

func (m *MockTemplateService) ListTemplate(ctx context.Context) (domain.Templates, error) {
	if m.ListTemplateFunc != nil {
		return m.ListTemplateFunc(ctx)
	}

	return nil, nil
}

// Implement other methods if needed...
type HandlerTestSuite struct {
	suite.Suite
	handler *Handler
	mockSrv *MockTemplateService
}

func (suite *HandlerTestSuite) SetupTest() {
	httpmock.Activate()
	suite.mockSrv = &MockTemplateService{}
	suite.handler = &Handler{tmplSrv: suite.mockSrv}
}

func (suite *HandlerTestSuite) TearDownTest() {
	httpmock.DeactivateAndReset()
}

func (suite *HandlerTestSuite) TestGetTemplate_Success() {
	expectedTemplate := &domain.Template{
		TemplateID:   "11111111-1111-1111-1111-111111111111",
		ConnectionID: "*************-2222-2222-************",
		// ... other fields ...
	}
	suite.mockSrv.RetrieveTemplateFunc = func(_ context.Context, _, _ string) (*domain.Template, error) {
		return expectedTemplate, nil
	}

	req := openapi.GetTemplateRequestObject{
		TemplateId:   expectedTemplate.TemplateID,
		ConnectionId: expectedTemplate.ConnectionID,
	}

	resp, err := suite.handler.GetTemplate(context.Background(), req)
	assert.NoError(suite.T(), err)
	successResp, ok := resp.(openapi.GetTemplate200JSONResponse)
	assert.True(suite.T(), ok)
	if assert.NotNil(suite.T(), successResp.TemplateId) {
		assert.Equal(suite.T(), expectedTemplate.TemplateID, *successResp.TemplateId)
	}
	if assert.NotNil(suite.T(), successResp.ConnectionId) {
		assert.Equal(suite.T(), expectedTemplate.ConnectionID, *successResp.ConnectionId)
	}
}

func (suite *HandlerTestSuite) TestGetTemplate_InvalidUUID() {
	req := openapi.GetTemplateRequestObject{
		TemplateId:   "invalid-uuid",
		ConnectionId: "invalid-uuid",
	}

	resp, err := suite.handler.GetTemplate(context.Background(), req)
	assert.NoError(suite.T(), err)
	_, ok := resp.(openapi.GetTemplate401JSONResponse)
	assert.True(suite.T(), ok)
}

func (suite *HandlerTestSuite) TestNewTemplate_ConflictError() {
	// Mock service to return a template conflict error
	suite.mockSrv.NewTemplateFunc = func(_ context.Context, _ *domain.Template) error {
		return &domain.TemplateConflictError{
			Message: "connection already exists",
		}
	}

	req := openapi.NewTemplateRequestObject{
		ConnectionId: "*************-2222-2222-************",
		Body: &openapi.TemplateResponse{
			Body: strPtr("Test template body"),
			Connection: &openapi.Connection{
				CountryCode: "US",
				PartyCode:   "DOM",
				CpoUrl:      "https://example.com/cpo",
			},
		},
	}

	resp, err := suite.handler.NewTemplate(context.Background(), req)
	assert.NoError(suite.T(), err)

	// Check that we get a 409 response
	conflictResp, ok := resp.(openapi.NewTemplate409JSONResponse)
	assert.True(suite.T(), ok)
	if assert.NotNil(suite.T(), conflictResp.StatusMessage) {
		assert.Equal(suite.T(), "connection already exists", *conflictResp.StatusMessage)
	}
}

func (suite *HandlerTestSuite) TestUpdateTemplate_ConflictError() {
	// Mock service to return a template conflict error
	suite.mockSrv.UpdateTemplateFunc = func(_ context.Context, _ *domain.Template) error {
		return &domain.TemplateConflictError{
			Message: "connection already exists",
		}
	}

	req := openapi.UpdateTemplateRequestObject{
		TemplateId:   "11111111-1111-1111-1111-111111111111",
		ConnectionId: "*************-2222-2222-************",
		Body: &openapi.TemplateResponse{
			Body: strPtr("Updated template body"),
			Connection: &openapi.Connection{
				CountryCode: "US",
				PartyCode:   "DOM",
				CpoUrl:      "https://example.com/cpo",
			},
		},
	}

	resp, err := suite.handler.UpdateTemplate(context.Background(), req)
	assert.NoError(suite.T(), err)

	// Check that we get a 409 response
	_, ok := resp.(openapi.UpdateTemplate409Response)
	assert.True(suite.T(), ok)
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}
