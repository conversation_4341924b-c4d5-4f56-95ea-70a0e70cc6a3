package resthttp

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
)

// TestEMSPValidationErrorResponse demonstrates the error response format
func TestEMSPValidationErrorResponse(t *testing.T) {
	// Setup mocks
	mockTemplateAPI := &MockTemplateAPI{}
	mockEMSPRepo := &MockEMSPConnectionRepository{}

	// Mock EMSP connection not found
	mockEMSPRepo.On("Exists", mock.Anything, "US", "INVALID", "https://invalid-url.com").Return(false, nil)

	// Create services
	emspValidator := service.NewEMSPConnectionValidator(mockEMSPRepo)
	templateSrv := service.NewTemplateSrv(mockTemplateAPI, emspValidator)
	handler := NewHandler(templateSrv)

	// Create request with invalid EMSP connection
	requestBody := openapi.TemplateResponse{
		Body: strPtr("Test template body"),
		Connection: &openapi.Connection{
			CountryCode: "US",
			PartyCode:   "INVALID",
			CpoUrl:      "https://invalid-url.com",
		},
	}

	requestBodyBytes, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/api/v1/templates/550e8400-e29b-41d4-a716-446655440000", bytes.NewReader(requestBodyBytes))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Execute the handler
	request := openapi.NewTemplateRequestObject{
		ConnectionId: "550e8400-e29b-41d4-a716-446655440000",
		Body:         &requestBody,
	}

	response, err := handler.NewTemplate(req.Context(), request)
	assert.NoError(t, err)

	// Handle the response
	switch resp := response.(type) {
	case openapi.NewTemplatedefaultJSONResponse:
		rr.WriteHeader(resp.StatusCode)
		rr.Header().Set("Content-Type", "application/json")
		json.NewEncoder(rr).Encode(resp.Body)
	default:
		t.Fatalf("Unexpected response type: %T", resp)
	}

	// Verify the response
	assert.Equal(t, http.StatusBadRequest, rr.Code)
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"))

	// Parse and verify error response
	var errorResponse openapi.ErrorResponse
	err = json.Unmarshal(rr.Body.Bytes(), &errorResponse)
	assert.NoError(t, err)
	assert.NotNil(t, errorResponse.StatusMessage)

	expectedMessage := "EMSP connection not found for country_code: US, party_id: INVALID, cpo_url: https://invalid-url.com"
	assert.Equal(t, expectedMessage, *errorResponse.StatusMessage)

	// Print the actual JSON response for demonstration
	t.Logf("Error Response JSON: %s", rr.Body.String())
	t.Logf("Error Message: %s", *errorResponse.StatusMessage)

	// Verify mocks
	mockEMSPRepo.AssertExpectations(t)
}

// TestEMSPValidationErrorResponseFormat shows the exact JSON format returned
func TestEMSPValidationErrorResponseFormat(t *testing.T) {
	// This test demonstrates the exact JSON format that will be returned to clients
	expectedJSON := `{
		"status_message": "EMSP connection not found for country_code: US, party_id: DOM, cpo_url: https://invalid-url.com"
	}`

	// Parse to verify it's valid JSON
	var errorResponse openapi.ErrorResponse
	err := json.Unmarshal([]byte(expectedJSON), &errorResponse)
	assert.NoError(t, err)
	assert.NotNil(t, errorResponse.StatusMessage)
	assert.Contains(t, *errorResponse.StatusMessage, "EMSP connection not found")

	t.Logf("Expected error response format:\n%s", expectedJSON)
}
