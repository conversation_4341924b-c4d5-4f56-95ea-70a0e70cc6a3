package resthttp

import (
	"encoding/json"
	"net/http"
	"time"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
)

func Success(w http.ResponseWriter, data any, status int) {
	response(w, data, status)
}

func response(w http.ResponseWriter, data any, httpStatus int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatus)

	if err := json.NewEncoder(w).Encode(&data); err != nil {
		http.Error(w, "internal server error", http.StatusInternalServerError)

		return
	}
}

func ToTemplateModel(template *openapi.TemplateResponse) *domain.Template {
	// Handle nil template
	if template == nil {
		return nil
	}

	// Create a new template with safe dereferencing
	result := &domain.Template{}

	// Safely dereference template fields
	if template.TemplateId != nil {
		result.TemplateID = *template.TemplateId
	}
	if template.ConnectionId != nil {
		result.ConnectionID = *template.ConnectionId
	}
	if template.IsDefault != nil {
		result.IsDefault = *template.IsDefault
	}
	if template.Body != nil {
		result.Body = *template.Body
	}

	// Handle Header fields
	if template.Header != nil {
		// Handle HeaderText
		if template.Header.HeaderText != nil {
			if template.Header.HeaderText.Font != nil {
				result.HeaderText.Font = *template.Header.HeaderText.Font
			}
			if template.Header.HeaderText.Style != nil {
				result.HeaderText.Style = *template.Header.HeaderText.Style
			}
			if template.Header.HeaderText.Color != nil {
				result.HeaderText.Color = *template.Header.HeaderText.Color
			}
			if template.Header.HeaderText.Value != nil {
				result.HeaderText.Value = *template.Header.HeaderText.Value
			}
		}

		// Handle HeaderIcon
		if template.Header.HeaderIcon != nil {
			if template.Header.HeaderIcon.Width != nil {
				result.HeaderIcon.Width = *template.Header.HeaderIcon.Width
			}
			if template.Header.HeaderIcon.Height != nil {
				result.HeaderIcon.Height = *template.Header.HeaderIcon.Height
			}
			if template.Header.HeaderIcon.Src != nil {
				result.HeaderIcon.Src = *template.Header.HeaderIcon.Src
			}
			if template.Header.HeaderIcon.Name != nil {
				result.HeaderIcon.Name = *template.Header.HeaderIcon.Name
			}
		}
	}

	// Handle Footer fields
	if template.Footer != nil {
		// Handle FooterText
		if template.Footer.FooterText != nil {
			if template.Footer.FooterText.Font != nil {
				result.FooterText.Font = *template.Footer.FooterText.Font
			}
			if template.Footer.FooterText.Style != nil {
				result.FooterText.Style = *template.Footer.FooterText.Style
			}
			if template.Footer.FooterText.Color != nil {
				result.FooterText.Color = *template.Footer.FooterText.Color
			}
			if template.Footer.FooterText.Value != nil {
				result.FooterText.Value = *template.Footer.FooterText.Value
			}
		}

		// Handle FooterIcon
		if template.Footer.FooterIcon != nil {
			if template.Footer.FooterIcon.Width != nil {
				result.FooterIcon.Width = *template.Footer.FooterIcon.Width
			}
			if template.Footer.FooterIcon.Height != nil {
				result.FooterIcon.Height = *template.Footer.FooterIcon.Height
			}
			if template.Footer.FooterIcon.Src != nil {
				result.FooterIcon.Src = *template.Footer.FooterIcon.Src
			}
			if template.Footer.FooterIcon.Name != nil {
				result.FooterIcon.Name = *template.Footer.FooterIcon.Name
			}
		}
	}

	// Handle Connection fields
	if template.Connection != nil {
		result.Connection.ConnectionName = template.Connection.ConnectionName
		result.Connection.CPOURL = template.Connection.CpoUrl
		result.Connection.ContryCode = template.Connection.CountryCode
		result.Connection.PartyCode = template.Connection.PartyCode
	}

	// Handle LastUpdated
	if template.LastUpdated != nil || template.LastUpdated == nil {
		result.LastUpdated = time.Now().Format(time.RFC3339)
	}

	return result
}

func ToOpenAPIModel(template *domain.Template) *openapi.TemplateResponse {
	// Handle nil template
	if template == nil {
		return nil
	}

	// Create a new response with safe pointer creation
	response := &openapi.TemplateResponse{
		TemplateId:   strPtr(template.TemplateID),
		ConnectionId: strPtr(template.ConnectionID),
		IsDefault:    boolPtr(template.IsDefault),
		Body:         strPtr(template.Body),
		Header: &openapi.Header{
			HeaderIcon: &openapi.Icon{
				Height: intPtr(template.HeaderIcon.Height),
				Width:  intPtr(template.HeaderIcon.Width),
				Src:    strPtr(template.HeaderIcon.Src),
				Name:   strPtr(template.HeaderIcon.Name),
			},
			HeaderText: &openapi.Text{
				Color: strPtr(template.HeaderText.Color),
				Font:  strPtr(template.HeaderText.Font),
				Style: strPtr(template.HeaderText.Style),
				Value: strPtr(template.HeaderText.Value),
			},
		},
		Footer: &openapi.Footer{
			FooterIcon: &openapi.Icon{
				Height: intPtr(template.FooterIcon.Height),
				Width:  intPtr(template.FooterIcon.Width),
				Src:    strPtr(template.FooterIcon.Src),
				Name:   strPtr(template.FooterIcon.Name),
			},
			FooterText: &openapi.Text{
				Color: strPtr(template.FooterText.Color),
				Font:  strPtr(template.FooterText.Font),
				Style: strPtr(template.FooterText.Style),
				Value: strPtr(template.FooterText.Value),
			},
		},
		Connection: &openapi.Connection{
			ConnectionName: template.Connection.ConnectionName,
			CpoUrl:         template.Connection.CPOURL,
			CountryCode:    template.Connection.ContryCode,
			PartyCode:      template.Connection.PartyCode,
		},
		LastUpdated: parseTimePtr(template.LastUpdated),
	}

	return response
}

// Helper to get pointer to string
func strPtr(s string) *string {
	return &s
}

// Helper to get pointer to bool
func boolPtr(b bool) *bool {
	return &b
}

// Helper to get pointer to int
func intPtr(i int) *int {
	return &i
}

// Helper to parse string to *time.Time
func parseTimePtr(s string) *time.Time {
	if s == "" {
		return nil
	}
	t, err := time.Parse(time.RFC3339, s)
	if err != nil {
		return nil
	}

	return &t
}
