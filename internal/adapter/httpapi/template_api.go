package httpapi

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/rs/zerolog/log"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

const (
	templateUrl        = "/api/v1/templates/"
	httpStatusConflict = 409
)

// TemplateAPI is an implementation of port.TemplateAPI
var _ port.TemplateAPI = (*TemplateAPI)(nil)

type TemplateAPI struct {
	Client *resty.Client
	Url    string
}

func NewTemplateAPI(url string, httpClient *resty.Client) *TemplateAPI {
	return &TemplateAPI{
		Client: httpClient,
		Url:    url,
	}
}

// buildURL constructs API URL based on provided parameters
func (t TemplateAPI) buildURL(connectionID, templateID string) string {
	if templateID == "" {
		return fmt.Sprintf("%s%s%s", t.Url, templateUrl, connectionID)
	}

	return fmt.Sprintf("%s%s%s/%s", t.Url, templateUrl, connectionID, templateID)
}

func (t TemplateAPI) GetTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	apiUrl := t.buildURL(connectionID, templateID)

	resp, err := t.Client.R().
		SetContext(ctx).
		Get(apiUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}
	if resp.IsError() {
		log.Error().Int("status", resp.StatusCode()).Str("operation", "get").Msg("template API request failed")

		return nil, fmt.Errorf("failed to get template: status %d", resp.StatusCode())
	}

	template := &domain.Template{}
	if err := json.Unmarshal(resp.Body(), template); err != nil {
		return nil, fmt.Errorf("failed to unmarshal template: %w", err)
	}

	return template, nil
}

func (t TemplateAPI) SaveTemplate(ctx context.Context, template *domain.Template) error {
	apiUrl := t.buildURL(template.ConnectionID, "")

	resp, err := t.Client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(template).
		Post(apiUrl)
	if err != nil {
		return fmt.Errorf("failed to save template: %w", err)
	}
	if resp.IsError() {
		log.Error().Int("status", resp.StatusCode()).Str("operation", "save").Msg("template API request failed")

		if resp.StatusCode() == httpStatusConflict {
			return &domain.TemplateConflictError{
				Message: "connection already exists",
			}
		}

		return fmt.Errorf("failed to save template: status %d", resp.StatusCode())
	}

	return nil
}

func (t TemplateAPI) UpdateTemplate(ctx context.Context, templateID, connectionID string, template *domain.Template) error {
	apiUrl := t.buildURL(connectionID, templateID)

	resp, err := t.Client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(template).
		Put(apiUrl)
	if err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}
	if resp.IsError() {
		log.Error().Int("status", resp.StatusCode()).Str("operation", "update").Msg("template API request failed")

		if resp.StatusCode() == httpStatusConflict {
			return &domain.TemplateConflictError{
				Message: "connection already exists",
			}
		}

		return fmt.Errorf("failed to update template: status %d", resp.StatusCode())
	}

	return nil
}

func (t TemplateAPI) DeleteTemplate(ctx context.Context, templateID, connectionID string) error {
	apiUrl := t.buildURL(connectionID, templateID)

	resp, err := t.Client.R().
		SetContext(ctx).
		Delete(apiUrl)
	if err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}
	if resp.IsError() {
		log.Error().Int("status", resp.StatusCode()).Str("operation", "delete").Msg("template API request failed")

		return fmt.Errorf("failed to delete template: status %d", resp.StatusCode())
	}

	return nil
}

func (t TemplateAPI) GetAllTemplates(ctx context.Context) (domain.Templates, error) {
	apiUrl := fmt.Sprintf("%s%s", t.Url, templateUrl[:len(templateUrl)-1]) // Remove trailing slash

	resp, err := t.Client.R().
		SetContext(ctx).
		Get(apiUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to get all templates: %w", err)
	}
	if resp.IsError() {
		log.Error().Int("status", resp.StatusCode()).Str("operation", "get-all").Msg("template API request failed")

		return nil, fmt.Errorf("failed to get all templates: status %d", resp.StatusCode())
	}

	var templates domain.Templates
	if err := json.Unmarshal(resp.Body(), &templates); err != nil {
		return nil, fmt.Errorf("failed to unmarshal templates: %w", err)
	}

	return templates, nil
}
