package httpapi_test

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

type TemplateAPITestSuite struct {
	suite.Suite
	api    *httpapi.TemplateAPI
	client *resty.Client
}

func (s *TemplateAPITestSuite) SetupTest() {
	s.client = resty.New()
	s.api = &httpapi.TemplateAPI{
		Client: s.client,
		Url:    "http://mockserver",
	}
	httpmock.ActivateNonDefault(s.client.GetClient())
}

func (s *TemplateAPITestSuite) TearDownTest() {
	httpmock.DeactivateAndReset()
}

func (s *TemplateAPITestSuite) TestGetTemplate_Success() {
	templateID := "d1057e57-f9ea-41ca-a2f5-ba56c9142e58"
	connectionID := uuid.New().String()
	expected := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		Body:         "test body",
		HeaderText:   domain.Text{Font: "Arial", Style: "Bold", Color: "#000000"},
		FooterText:   domain.Text{Font: "Arial", Style: "Italic", Color: "#000000"},
		HeaderIcon:   domain.Icon{Width: 100, Height: 100},
		FooterIcon:   domain.Icon{Width: 100, Height: 100},
	}

	respBody, _ := json.Marshal(expected)

	url := fmt.Sprintf("%s/api/v1/templates/%s/%s", s.api.Url, connectionID, templateID)
	httpmock.RegisterResponder("GET", url,
		httpmock.NewBytesResponder(200, respBody))

	got, err := s.api.GetTemplate(context.Background(), templateID, connectionID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), expected.TemplateID, got.TemplateID)
	require.Equal(s.T(), expected.ConnectionID, got.ConnectionID)
}

func (s *TemplateAPITestSuite) TestSaveTemplate_Success() {
	connectionID := uuid.New().String()
	tmpl := &domain.Template{TemplateID: uuid.New().String(), ConnectionID: connectionID}

	url := fmt.Sprintf("%s/api/v1/templates/%s", s.api.Url, connectionID)
	httpmock.RegisterResponder("POST", url,
		httpmock.NewStringResponder(201, ""))

	err := s.api.SaveTemplate(context.Background(), tmpl)
	require.NoError(s.T(), err)
}

func (s *TemplateAPITestSuite) TestUpdateTemplate_Success() {
	templateID := uuid.New().String()
	connectionID := uuid.New().String()
	tmpl := &domain.Template{TemplateID: templateID, ConnectionID: connectionID}

	url := fmt.Sprintf("%s/api/v1/templates/%s/%s", s.api.Url, connectionID, templateID)
	httpmock.RegisterResponder("PUT", url,
		httpmock.NewStringResponder(204, ""))

	err := s.api.UpdateTemplate(context.Background(), templateID, connectionID, tmpl)
	require.NoError(s.T(), err)
}

func (s *TemplateAPITestSuite) TestDeleteTemplate_Success() {
	templateID := uuid.New().String()
	connectionID := uuid.New().String()

	url := fmt.Sprintf("%s/api/v1/templates/%s/%s", s.api.Url, connectionID, templateID)
	httpmock.RegisterResponder("DELETE", url,
		httpmock.NewStringResponder(204, ""))

	err := s.api.DeleteTemplate(context.Background(), templateID, connectionID)
	require.NoError(s.T(), err)
}

func TestTemplateAPITestSuite(t *testing.T) {
	suite.Run(t, new(TemplateAPITestSuite))
}
