package mongodb

import (
	"context"
	"errors"
	"fmt"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

// EMSPConnectionRepository implements the port.EMSPConnectionRepository interface
var _ port.EMSPConnectionRepository = (*EMSPConnectionRepository)(nil)

type EMSPConnectionRepository struct {
	client     *mongo.Client
	database   string
	collection string
}

// NewEMSPConnectionRepository creates a new instance of EMSPConnectionRepository
func NewEMSPConnectionRepository(client *mongo.Client, cfg *config.Mongodb) *EMSPConnectionRepository {
	return &EMSPConnectionRepository{
		client:     client,
		database:   cfg.Database,
		collection: cfg.Collection,
	}
}

// Exists checks if an EMSP connection exists with the given parameters
func (r *EMSPConnectionRepository) Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error) {
	collection := r.client.Database(r.database).Collection(r.collection)
	filter := bson.D{
		{Key: "party_code", Value: bson.D{{Key: "$regex", Value: partyID}, {Key: "$options", Value: "i"}}},
		{Key: "country_code", Value: bson.D{{Key: "$regex", Value: countryCode}, {Key: "$options", Value: "i"}}},
		{Key: "cpo_url", Value: cpoURL},
	}
	log.Info().Any("filter", filter).Msg("database filter")
	err := collection.FindOne(ctx, filter).Err()
	if err != nil {
		log.Err(err).Msg("error to find one")
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil
		}

		return false, fmt.Errorf("failed to check EMSP connection existence: %w", err)
	}

	return true, nil
}
