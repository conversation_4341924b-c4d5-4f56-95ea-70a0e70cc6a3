package mongodb

import (
	"context"
	"encoding/json"
	"os"
	"path"
	"testing"

	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
)

type TemplateRepositorySuite struct {
	suite.Suite
	db       *mongo.Database
	mongoC   *mongodb.MongoDBContainer
	client   *mongo.Client
	cfg      *config.Configs
	connRepo *EMSPConnectionRepository
	ctx      context.Context
}

func (suite *TemplateRepositorySuite) SetupSuite() {
	ctx := context.Background()
	cfg := config.Load()
	suite.cfg = cfg
	suite.ctx = ctx
	mongodbContainer, err := mongodb.Run(ctx, "mongo:latest")
	suite.Require().NoError(err, "failed to setup mongo container")
	endpoint, err := mongodbContainer.ConnectionString(ctx)
	suite.Require().NoError(err, "failed to get connection string: %s", endpoint)
	mongoClient, err := mongo.Connect(options.Client().ApplyURI(endpoint))
	suite.Require().NoError(err, "failed to connect to MongoDB: %s", err)
	suite.client = mongoClient
	suite.db = mongoClient.Database(cfg.MongoDB.Database)
	suite.mongoC = mongodbContainer
	suite.connRepo = NewEMSPConnectionRepository(suite.client, suite.cfg.MongoDB)
	suite.insertRecord() // insert records
}

func (suite *TemplateRepositorySuite) readTestFileData(fileName string) ([]byte, error) {
	data, err := os.ReadFile(path.Join("../../../", "./tests/testdata", fileName))
	if err != nil {
		return nil, err
	}

	return data, nil
}
func (suite *TemplateRepositorySuite) insertRecord() {
	domData, err := suite.readTestFileData("us-dom.json")
	suite.Require().Nil(err)
	suite.Require().NotEmpty(domData)

	elvData, errElv := suite.readTestFileData("us-elv.json")
	suite.Require().Nil(errElv)
	suite.Require().NotEmpty(elvData)

	var domDoc map[string]any
	errDomUnmarsal := json.Unmarshal(domData, &domDoc)
	suite.Require().Nil(errDomUnmarsal)
	suite.Require().NotEmpty(domDoc)

	var elvDoc map[string]any
	errElvUnmarshal := json.Unmarshal(elvData, &elvDoc)
	suite.Require().Nil(errElvUnmarshal)
	suite.Require().NotEmpty(elvDoc)

	_, errDom := suite.db.Collection(suite.cfg.MongoDB.Collection).InsertOne(suite.ctx, domDoc)
	suite.Require().Nil(errDom)
	_, errElvDB := suite.db.Collection(suite.cfg.MongoDB.Collection).InsertOne(suite.ctx, elvDoc)
	suite.Require().Nil(errElvDB)
}

func (suite *TemplateRepositorySuite) TestExists() {
	suite.Run("not found connection", func() {
		exits, err := suite.connRepo.Exists(suite.ctx, "us", "dom", "http://example.com/ocpi/incorrect-url")
		suite.Require().Nil(err)
		suite.Require().False(exits)
	})

	suite.Run("found connection dom", func() {
		exits, err := suite.connRepo.Exists(suite.ctx, "us", "dom", "http://example.com/ocpi/cpo/dom")
		suite.Require().Nil(err)
		suite.Require().True(exits)
	})

	suite.Run("found connection elv", func() {
		exits, err := suite.connRepo.Exists(suite.ctx, "us", "elv", "http://example.com/ocpi/cpo/elv")
		suite.Require().Nil(err)
		suite.Require().True(exits)
	})
}

func TestTemplateRepositorySuite(t *testing.T) {
	suite.Run(t, new(TemplateRepositorySuite))
}
