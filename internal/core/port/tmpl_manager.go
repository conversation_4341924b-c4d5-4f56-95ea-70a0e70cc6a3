package port

import (
	"context"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

type TemplateManager interface {
	ListTemplate(ctx context.Context) (domain.Templates, error)
	RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error)
	NewTemplate(ctx context.Context, template *domain.Template) error
	UpdateTemplate(ctx context.Context, template *domain.Template) error
	RemoveTemplate(ctx context.Context, templateID, connectionID string) error
}

type TemplateAPI interface {
	GetAllTemplates(ctx context.Context) (domain.Templates, error)
	GetTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error)
	SaveTemplate(ctx context.Context, template *domain.Template) error
	UpdateTemplate(ctx context.Context, templateID, connectionID string, template *domain.Template) error
	DeleteTemplate(ctx context.Context, templateID, connectionID string) error
}
