package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

// TestMandatoryFieldsValidation demonstrates that all three fields are mandatory
func TestMandatoryFieldsValidation(t *testing.T) {
	tests := []struct {
		name          string
		countryCode   string
		partyID       string
		cpoURL        string
		expectedError string
		description   string
	}{
		{
			name:          "All fields provided - should validate",
			countryCode:   "US",
			partyID:       "DOM",
			cpoURL:        "https://test.com",
			expectedError: "", // No error expected, will be handled by repository
			description:   "When all fields are provided, validation should proceed to repository check",
		},
		{
			name:          "Missing country code",
			countryCode:   "",
			partyID:       "DOM",
			cpoURL:        "https://test.com",
			expectedError: "country_code is required",
			description:   "Empty country code should return validation error",
		},
		{
			name:          "Missing party ID",
			countryCode:   "US",
			partyID:       "",
			cpoURL:        "https://test.com",
			expectedError: "party_id is required",
			description:   "Empty party ID should return validation error",
		},
		{
			name:          "Missing CPO URL",
			countryCode:   "US",
			partyID:       "DOM",
			cpoURL:        "",
			expectedError: "cpo_url is required",
			description:   "Empty CPO URL should return validation error",
		},
		{
			name:          "All fields empty",
			countryCode:   "",
			partyID:       "",
			cpoURL:        "",
			expectedError: "country_code is required",
			description:   "When all fields are empty, should fail on first field (country_code)",
		},
		{
			name:          "Only country code provided",
			countryCode:   "US",
			partyID:       "",
			cpoURL:        "",
			expectedError: "party_id is required",
			description:   "When only country code is provided, should fail on party_id",
		},
		{
			name:          "Only country code and party ID provided",
			countryCode:   "US",
			partyID:       "DOM",
			cpoURL:        "",
			expectedError: "cpo_url is required",
			description:   "When only country code and party ID are provided, should fail on cpo_url",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockEMSPConnectionRepository{}

			// Only set up mock if we expect to reach the repository (all fields provided)
			if tt.expectedError == "" {
				mockRepo.On("Exists", context.Background(), tt.countryCode, tt.partyID, tt.cpoURL).Return(false, nil)
			}

			validator := NewEMSPConnectionValidator(mockRepo)
			err := validator.ValidateConnection(context.Background(), tt.countryCode, tt.partyID, tt.cpoURL)

			if tt.expectedError != "" {
				// Should get a validation error
				assert.Error(t, err, tt.description)
				assert.True(t, domain.IsEMSPConnectionValidationError(err), "Should be a validation error")
				assert.Equal(t, tt.expectedError, err.Error(), "Error message should match expected")
			} else {
				// Should proceed to repository check (which will fail in this test)
				assert.Error(t, err, "Should get repository validation error")
				assert.True(t, domain.IsEMSPConnectionValidationError(err), "Should be a validation error")
				assert.Contains(t, err.Error(), "EMSP connection not found", "Should be connection not found error")
			}

			mockRepo.AssertExpectations(t)

			t.Logf("✓ %s", tt.description)
		})
	}
}

// TestFieldValidationOrder demonstrates the order in which fields are validated
func TestFieldValidationOrder(t *testing.T) {
	mockRepo := &MockEMSPConnectionRepository{}
	validator := NewEMSPConnectionValidator(mockRepo)

	// Test that country_code is validated first
	err := validator.ValidateConnection(context.Background(), "", "", "")
	assert.Error(t, err)
	assert.Equal(t, "country_code is required", err.Error())

	// Test that party_id is validated second
	err = validator.ValidateConnection(context.Background(), "US", "", "")
	assert.Error(t, err)
	assert.Equal(t, "party_id is required", err.Error())

	// Test that cpo_url is validated third
	err = validator.ValidateConnection(context.Background(), "US", "DOM", "")
	assert.Error(t, err)
	assert.Equal(t, "cpo_url is required", err.Error())

	t.Log("✓ Field validation follows correct order: country_code -> party_id -> cpo_url")
}

// TestValidationErrorType demonstrates that all validation errors are of the correct type
func TestValidationErrorType(t *testing.T) {
	mockRepo := &MockEMSPConnectionRepository{}
	validator := NewEMSPConnectionValidator(mockRepo)

	testCases := []struct {
		name        string
		countryCode string
		partyID     string
		cpoURL      string
	}{
		{"Missing country_code", "", "DOM", "https://test.com"},
		{"Missing party_id", "US", "", "https://test.com"},
		{"Missing cpo_url", "US", "DOM", ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := validator.ValidateConnection(context.Background(), tc.countryCode, tc.partyID, tc.cpoURL)

			assert.Error(t, err)
			assert.True(t, domain.IsEMSPConnectionValidationError(err), "Should be EMSPConnectionValidationError type")

			// Cast to validation error and check fields
			validationErr := &domain.EMSPConnectionValidationError{}
			if errors.As(err, &validationErr) {
				assert.Equal(t, tc.countryCode, validationErr.CountryCode)
				assert.Equal(t, tc.partyID, validationErr.PartyID)
				assert.Equal(t, tc.cpoURL, validationErr.CPOURL)
				assert.NotEmpty(t, validationErr.Message)
			}
		})
	}

	t.Log("✓ All validation errors are of type EMSPConnectionValidationError")
}
