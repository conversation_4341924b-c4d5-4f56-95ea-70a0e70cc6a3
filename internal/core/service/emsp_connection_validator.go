package service

import (
	"context"
	"fmt"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

// EMSPConnectionValidator provides validation services for EMSP connections
type EMSPConnectionValidator struct {
	emspRepo port.EMSPConnectionRepository
}

// NewEMSPConnectionValidator creates a new instance of EMSPConnectionValidator
func NewEMSPConnectionValidator(emspRepo port.EMSPConnectionRepository) *EMSPConnectionValidator {
	return &EMSPConnectionValidator{
		emspRepo: emspRepo,
	}
}

// ValidateConnection validates if an EMSP connection exists with the given parameters
func (v *EMSPConnectionValidator) ValidateConnection(ctx context.Context, countryCode, partyID, cpoURL string) error {
	if countryCode == "" {
		return &domain.EMSPConnectionValidationError{
			CountryCode: countryCode,
			PartyID:     partyID,
			CPOURL:      cpoURL,
			Message:     "country_code is required",
		}
	}

	if partyID == "" {
		return &domain.EMSPConnectionValidationError{
			CountryCode: countryCode,
			PartyID:     partyID,
			CPOURL:      cpoURL,
			Message:     "party_id is required",
		}
	}

	if cpoURL == "" {
		return &domain.EMSPConnectionValidationError{
			CountryCode: countryCode,
			PartyID:     partyID,
			CPOURL:      cpoURL,
			Message:     "cpo_url is required",
		}
	}

	exists, err := v.emspRepo.Exists(ctx, countryCode, partyID, cpoURL)
	if err != nil {
		return fmt.Errorf("failed to validate EMSP connection: %w", err)
	}

	fmt.Println("Exists:", exists)

	if !exists {
		return &domain.EMSPConnectionValidationError{
			CountryCode: countryCode,
			PartyID:     partyID,
			CPOURL:      cpoURL,
			Message:     fmt.Sprintf("EMSP connection not found for country_code: %s, party_id: %s, cpo_url: %s", countryCode, partyID, cpoURL),
		}
	}

	return nil
}
