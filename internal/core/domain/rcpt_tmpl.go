package domain

type Templates []Template

type Template struct {
	TemplateID   string     `json:"template_id"`
	ConnectionID string     `json:"connection_id"`
	IsDefault    bool       `json:"is_default"`
	Body         string     `json:"body"`
	HeaderText   Text       `json:"header_text"`
	FooterText   Text       `json:"footer_text"`
	HeaderIcon   Icon       `json:"header_logo"`
	FooterIcon   Icon       `json:"footer_logo"`
	Connection   Connection `json:"connection"`
	LastUpdated  string     `json:"last_updated"`
}

type Text struct {
	Font  string `json:"font"`
	Style string `json:"style"`
	Color string `json:"color"`
	Value string `json:"value"`
}

type Icon struct {
	Src    string `json:"src"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Name   string `json:"name"`
}

type Connection struct {
	ConnectionName string `json:"connection_name"`
	CPOURL         string `json:"cpo_url"`
	ContryCode     string `json:"country_code"`
	PartyCode      string `json:"party_code"`
}

// EMSPConnection represents the EMSP connection structure stored in MongoDB
type EMSPConnection struct {
	CountryCode string                     `json:"country_code" bson:"country_code"`
	CPOURL      string                     `json:"cpo_url" bson:"cpo_url"`
	Endpoint    map[string]EndpointVersion `json:"endpoint" bson:"endpoint"`
	PartyCode   string                     `json:"party_code" bson:"party_code"`
	Credential  Credential                 `json:"credential" bson:"credential"`
}

// EndpointVersion represents the version-specific endpoint configuration
type EndpointVersion struct {
	Version   string     `json:"version" bson:"version"`
	Endpoints []Endpoint `json:"endpoints" bson:"endpoints"`
}

// Endpoint represents individual endpoint configuration
type Endpoint struct {
	Identifier string `json:"identifier" bson:"identifier"`
	URL        string `json:"url" bson:"url"`
	Role       string `json:"role" bson:"role"`
}

// Credential represents the credential information for both EMSP and CPO
type Credential struct {
	EMSP CredentialInfo `json:"emsp" bson:"emsp"`
	CPO  CredentialInfo `json:"cpo" bson:"cpo"`
}

// CredentialInfo represents the credential details
type CredentialInfo struct {
	Token string `json:"token" bson:"token"`
	URL   string `json:"url" bson:"url"`
	Roles []Role `json:"roles" bson:"roles"`
}

// Role represents the role information
type Role struct {
	Role            string          `json:"role" bson:"role"`
	PartyID         string          `json:"party_id" bson:"party_id"`
	CountryCode     string          `json:"country_code" bson:"country_code"`
	BusinessDetails BusinessDetails `json:"business_details" bson:"business_details"`
}

// BusinessDetails represents business information
type BusinessDetails struct {
	Name    string `json:"name" bson:"name"`
	Logo    Logo   `json:"logo" bson:"logo"`
	Website string `json:"website" bson:"website"`
}

// Logo represents logo information
type Logo struct {
	Category  string `json:"category" bson:"category"`
	Height    int    `json:"height" bson:"height"`
	Thumbnail string `json:"thumbnail" bson:"thumbnail"`
	Type      string `json:"type" bson:"type"`
	URL       string `json:"url" bson:"url"`
	Width     int    `json:"width" bson:"width"`
}
