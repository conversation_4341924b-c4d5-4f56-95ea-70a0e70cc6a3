package domain

import "errors"

var (
	ErrUserAlreadyExists = errors.New("user already exists")

	ErrUnableToFetchScopes = errors.New("unable to fetch scopes from lookup api")
	ErrForbiddenAction     = errors.New("user is not allowed to execute action")
	ErrForbiddenResource   = errors.New("user is not allowed to access resource")

	// EMSP Connection validation errors
	ErrEMSPConnectionNotFound = errors.New("EMSP connection not found")
	ErrInvalidConnectionData  = errors.New("invalid connection data")

	// Template conflict errors
	ErrTemplateAlreadyExists = errors.New("template already exists")
)

// EMSPConnectionValidationError represents an EMSP connection validation error
type EMSPConnectionValidationError struct {
	CountryCode string
	PartyID     string
	CPOURL      string
	Message     string
}

func (e *EMSPConnectionValidationError) Error() string {
	return e.Message
}

// IsEMSPConnectionValidationError checks if an error is an EMSP connection validation error
func IsEMSPConnectionValidationError(err error) bool {
	eMSPConnectionValidationError := &EMSPConnectionValidationError{}
	ok := errors.As(err, &eMSPConnectionValidationError)

	return ok
}

// TemplateConflictError represents a template conflict error (409)
type TemplateConflictError struct {
	Message string
}

func (e *TemplateConflictError) Error() string {
	return e.Message
}

// IsTemplateConflictError checks if an error is a template conflict error
func IsTemplateConflictError(err error) bool {
	templateConflictError := &TemplateConflictError{}
	ok := errors.As(err, &templateConflictError)

	return ok
}
