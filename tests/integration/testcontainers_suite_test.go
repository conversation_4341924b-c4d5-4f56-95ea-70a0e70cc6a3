package integration

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"github.com/testcontainers/testcontainers-go/wait"
)

// TestContainersSuite is a test suite that sets up all required containers
type TestContainersSuite struct {
	suite.Suite
	ctx               context.Context
	mongoContainer    *mongodb.MongoDBContainer
	mockAPIContainer  testcontainers.Container
	wiremockContainer testcontainers.Container
	mongoURI          string
	mockAPIURL        string
	wiremockURL       string
}

// SetupSuite starts all containers needed for the tests
func (s *TestContainersSuite) SetupSuite() {
	s.ctx = context.Background()

	// Start MongoDB container
	mongoContainer, err := mongodb.RunContainer(s.ctx,
		testcontainers.WithImage("mongo:6.0.10"),
		mongodb.WithUsername("root"),
		mongodb.WithPassword("example"),
	)
	s.Require().NoError(err)
	s.mongoContainer = mongoContainer

	// Get MongoDB connection string
	mongoURI, err := mongoContainer.ConnectionString(s.ctx)
	s.Require().NoError(err)
	s.mongoURI = mongoURI

	// Start mock API container
	mockAPIContainer, mockAPIURL := s.startMockAPIContainer()
	s.mockAPIContainer = mockAPIContainer
	s.mockAPIURL = mockAPIURL

	// Start Wiremock container
	wiremockContainer, wiremockURL := s.startWiremockContainer()
	s.wiremockContainer = wiremockContainer
	s.wiremockURL = wiremockURL
}

// TearDownSuite stops all containers after tests
func (s *TestContainersSuite) TearDownSuite() {
	if s.mongoContainer != nil {
		s.mongoContainer.Terminate(s.ctx)
	}

	if s.mockAPIContainer != nil {
		s.mockAPIContainer.Terminate(s.ctx)
	}

	if s.wiremockContainer != nil {
		s.wiremockContainer.Terminate(s.ctx)
	}
}

// startMockAPIContainer starts a container with a mock API for testing
func (s *TestContainersSuite) startMockAPIContainer() (testcontainers.Container, string) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "golang:1.22",
		ExposedPorts: []string{"8080/tcp"},
		Cmd: []string{"sh", "-c", `
cat <<EOF > /server.go
package main
import (
    "encoding/json"
    "io"
    "net/http"
    "strings"
)

type Text struct {
    Font  string
    Style string
    Color string
}

type Icon struct {
    Src    string
    Width  int
    Height int
}

type Template struct {
    TemplateID   string
    ConnectionID string
    IsDefault    bool
    Body         string
    HeaderText   Text
    FooterText   Text
    HeaderIcon   Icon
    FooterIcon   Icon
}

func main() {
    http.HandleFunc("/api/v1/templates/", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")

        pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/v1/templates/"), "/")
        connectionID := pathParts[0]

        var templateID string
        if len(pathParts) > 1 {
            templateID = pathParts[1]
        }

        switch r.Method {
        case "GET":
            template := Template{
                TemplateID:   templateID,
                ConnectionID: connectionID,
                IsDefault:    true,
                Body:         "Receipt template body",
                HeaderText: Text{
                    Font:  "Arial",
                    Style: "Bold",
                    Color: "#000000",
                },
                FooterText: Text{
                    Font:  "Arial",
                    Style: "Italic",
                    Color: "#333333",
                },
                HeaderIcon: Icon{
                    Src:    "header.png",
                    Width:  100,
                    Height: 50,
                },
                FooterIcon: Icon{
                    Src:    "footer.png",
                    Width:  80,
                    Height: 40,
                },
            }
            json.NewEncoder(w).Encode(template)
        case "POST":
            body, _ := io.ReadAll(r.Body)
            var template Template
            json.Unmarshal(body, &template)
            w.WriteHeader(http.StatusCreated)
            json.NewEncoder(w).Encode(template)
        case "PUT":
            w.WriteHeader(http.StatusNoContent)
        case "DELETE":
            w.WriteHeader(http.StatusNoContent)
        }
    })

    // Health endpoint
    http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("OK"))
    })

    http.ListenAndServe(":8080", nil)
}
EOF
go run /server.go
`},
		WaitingFor: wait.ForListeningPort("8080/tcp"),
	}

	container, err := testcontainers.GenericContainer(s.ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})
	s.Require().NoError(err)

	host, err := container.Host(s.ctx)
	s.Require().NoError(err)

	port, err := container.MappedPort(s.ctx, "8080")
	s.Require().NoError(err)

	baseURL := fmt.Sprintf("http://%s:%s", host, port.Port())

	// Wait for the server to be ready
	s.waitForServer(baseURL)

	return container, baseURL
}

// startWiremockContainer starts a Wiremock container for testing
func (s *TestContainersSuite) startWiremockContainer() (testcontainers.Container, string) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "wiremock/wiremock:latest",
		ExposedPorts: []string{"8080/tcp"},
		WaitingFor:   wait.ForHTTP("/__admin").WithPort("8080/tcp"),
	}

	container, err := testcontainers.GenericContainer(s.ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})
	s.Require().NoError(err)

	host, err := container.Host(s.ctx)
	s.Require().NoError(err)

	port, err := container.MappedPort(s.ctx, "8080")
	s.Require().NoError(err)

	baseURL := fmt.Sprintf("http://%s:%s", host, port.Port())

	return container, baseURL
}

// waitForServer waits for the mock API server to be ready
func (s *TestContainersSuite) waitForServer(baseURL string) {
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(200 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			s.T().Fatal("server did not start in time")
		case <-ticker.C:
			client := &http.Client{
				Timeout: 2 * time.Second,
			}
			resp, err := client.Get(baseURL + "/health")
			if err == nil {
				resp.Body.Close()
				if resp.StatusCode == http.StatusOK {
					return
				}
			}
		}
	}
}

// TestTemplateServiceWithContainers tests the template service with all containers
func (s *TestContainersSuite) TestTemplateServiceWithContainers() {
	// This test would use all the containers set up in the suite
	s.T().Log("MongoDB URI:", s.mongoURI)
	s.T().Log("Mock API URL:", s.mockAPIURL)
	s.T().Log("Wiremock URL:", s.wiremockURL)

	// Here you would initialize your application with the container URLs
	// and run tests against it
}

// TestRunSuite runs the test suite
func TestRunSuite(t *testing.T) {
	suite.Run(t, new(TestContainersSuite))
}
