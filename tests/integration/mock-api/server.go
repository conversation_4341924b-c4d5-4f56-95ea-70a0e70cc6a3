package main

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

type Text struct {
	Font  string `json:"font"`
	Style string `json:"style"`
	Color string `json:"color"`
}

type Icon struct {
	Src    string `json:"src"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type Template struct {
	TemplateID   string `json:"template_id"`
	ConnectionID string `json:"connection_id"`
	IsDefault    bool   `json:"is_default"`
	Body         string `json:"body"`
	HeaderText   Text   `json:"header_text"`
	FooterText   Text   `json:"footer_text"`
	HeaderIcon   Icon   `json:"header_icon"`
	FooterIcon   Icon   `json:"footer_icon"`
}

func main() {
	http.HandleFunc("/api/v1/templates/", handleTemplates)
	http.HandleFunc("/health", handleHealth)

	srv := &http.Server{
		Addr:         ":8080",
		Handler:      nil, // or your handler
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	log.Println("Starting mock template API server on :8080")
	if err := srv.ListenAndServe(); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}

func handleHealth(w http.ResponseWriter, _ *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func handleTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Parse path to extract connectionID and templateID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/templates/")
	parts := strings.Split(path, "/")

	connectionID := parts[0]
	var templateID string
	if len(parts) > 1 {
		templateID = parts[1]
	}

	log.Printf("%s %s", r.Method, r.URL.Path)

	switch r.Method {
	case http.MethodGet:
		// Return a template
		template := Template{
			TemplateID:   templateID,
			ConnectionID: connectionID,
			IsDefault:    true,
			Body:         "Receipt template body",
			HeaderText: Text{
				Font:  "Arial",
				Style: "Bold",
				Color: "#000000",
			},
			FooterText: Text{
				Font:  "Arial",
				Style: "Italic",
				Color: "#333333",
			},
			HeaderIcon: Icon{
				Src:    "header.png",
				Width:  100,
				Height: 50,
			},
			FooterIcon: Icon{
				Src:    "footer.png",
				Width:  80,
				Height: 40,
			},
		}
		json.NewEncoder(w).Encode(template)

	case http.MethodPost:
		// Create a new template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)

			return
		}

		var template Template
		if err = json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)

			return
		}

		log.Printf("Created template: %+v", template)

		// Set response status
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(template)

	case http.MethodPut:
		// Update a template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)

			return
		}

		var template Template
		if err = json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)

			return
		}

		log.Printf("Updated template: %+v", template)

		// Set response status
		w.WriteHeader(http.StatusNoContent)

	case http.MethodDelete:
		// Delete a template
		log.Printf("Deleted template: %s/%s", connectionID, templateID)
		w.WriteHeader(http.StatusNoContent)

	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}
