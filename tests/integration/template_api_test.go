package integration

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

// TestTemplateAPI_Integration tests the template API adapter with a mock backend
func TestTemplateAPI_Integration(t *testing.T) {
	ctx := context.Background()

	// Start a mock API container
	mockAPIContainer, err := startMockTemplateAPIContainer(ctx)
	require.NoError(t, err)
	defer mockAPIContainer.Terminate(ctx)

	// Get the mock API URL
	mockAPIHost, err := mockAPIContainer.Host(ctx)
	require.NoError(t, err)
	mockAPIPort, err := mockAPIContainer.MappedPort(ctx, "8080")
	require.NoError(t, err)
	mockAPIURL := "http://" + mockAPIHost + ":" + mockAPIPort.Port()

	// Wait for the mock API to be ready
	waitForAPIServer(t, mockAPIURL+"/health")

	// Create resty client and API adapter
	client := resty.New().SetTimeout(5 * time.Second)
	templateAPI := httpapi.NewTemplateAPI(mockAPIURL, client)

	// Test data
	connectionID := uuid.NewString()
	templateID := uuid.NewString()
	template := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "Test receipt body",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "Bold",
			Color: "#000000",
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "Italic",
			Color: "#333333",
		},
		HeaderIcon: domain.Icon{
			Src:    "header.png",
			Width:  100,
			Height: 50,
		},
		FooterIcon: domain.Icon{
			Src:    "footer.png",
			Width:  80,
			Height: 40,
		},
	}

	// Test GetTemplate
	t.Run("GetTemplate", func(t *testing.T) {
		retrievedTemplate, err := templateAPI.GetTemplate(ctx, templateID, connectionID)
		require.NoError(t, err)
		assert.Equal(t, templateID, retrievedTemplate.TemplateID)
		assert.Equal(t, connectionID, retrievedTemplate.ConnectionID)
		assert.Equal(t, true, retrievedTemplate.IsDefault)
	})

	// Test SaveTemplate
	t.Run("SaveTemplate", func(t *testing.T) {
		err = templateAPI.SaveTemplate(ctx, template)
		require.NoError(t, err)
	})

	// Test UpdateTemplate
	t.Run("UpdateTemplate", func(t *testing.T) {
		err = templateAPI.UpdateTemplate(ctx, templateID, connectionID, template)
		require.NoError(t, err)
	})

	// Test DeleteTemplate
	t.Run("DeleteTemplate", func(t *testing.T) {
		err = templateAPI.DeleteTemplate(ctx, templateID, connectionID)
		require.NoError(t, err)
	})
}

// startMockTemplateAPIContainer starts a container with a mock template API
func startMockTemplateAPIContainer(ctx context.Context) (testcontainers.Container, error) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "golang:1.22",
		ExposedPorts: []string{"8080/tcp"},
		Cmd: []string{"sh", "-c", `cat > /server.go << 'EOF'
package main

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"
)

type Text struct {
	Font  string ` + "`json:\"font\"`" + `
	Style string ` + "`json:\"style\"`" + `
	Color string ` + "`json:\"color\"`" + `
}

type Icon struct {
	Src    string ` + "`json:\"src\"`" + `
	Width  int    ` + "`json:\"width\"`" + `
	Height int    ` + "`json:\"height\"`" + `
}

type Template struct {
	TemplateID   string ` + "`json:\"template_id\"`" + `
	ConnectionID string ` + "`json:\"connection_id\"`" + `
	IsDefault    bool   ` + "`json:\"is_default\"`" + `
	Body         string ` + "`json:\"body\"`" + `
	HeaderText   Text   ` + "`json:\"header_text\"`" + `
	FooterText   Text   ` + "`json:\"footer_text\"`" + `
	HeaderIcon   Icon   ` + "`json:\"header_icon\"`" + `
	FooterIcon   Icon   ` + "`json:\"footer_icon\"`" + `
}

func main() {
	http.HandleFunc("/api/v1/templates/", handleTemplates)
	http.HandleFunc("/health", handleHealth)
	
	log.Println("Starting server on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func handleTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Parse path to extract connectionID and templateID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/templates/")
	parts := strings.Split(path, "/")
	
	connectionID := parts[0]
	var templateID string
	if len(parts) > 1 {
		templateID = parts[1]
	}
	
	switch r.Method {
	case http.MethodGet:
		// Return a template
		template := Template{
			TemplateID:   templateID,
			ConnectionID: connectionID,
			IsDefault:    true,
			Body:         "Receipt template body",
			HeaderText: Text{
				Font:  "Arial",
				Style: "Bold",
				Color: "#000000",
			},
			FooterText: Text{
				Font:  "Arial",
				Style: "Italic",
				Color: "#333333",
			},
			HeaderIcon: Icon{
				Src:    "header.png",
				Width:  100,
				Height: 50,
			},
			FooterIcon: Icon{
				Src:    "footer.png",
				Width:  80,
				Height: 40,
			},
		}
		json.NewEncoder(w).Encode(template)
		
	case http.MethodPost:
		// Create a new template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(template)
		
	case http.MethodPut:
		// Update a template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusNoContent)
		
	case http.MethodDelete:
		// Delete a template
		w.WriteHeader(http.StatusNoContent)
		
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}
EOF

go run /server.go`},
		WaitingFor: wait.ForHTTP("/health").WithPort("8080/tcp"),
	}

	return testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})
}

// waitForAPIServer waits for the API server to be ready
func waitForAPIServer(t *testing.T, healthURL string) {
	maxRetries := 20
	for i := 0; i < maxRetries; i++ {
		resp, err := http.Get(healthURL)
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()

			return
		}
		if resp != nil {
			resp.Body.Close()
		}
		t.Logf("Waiting for API server to start (attempt %d/%d)", i+1, maxRetries)
		time.Sleep(500 * time.Millisecond)
	}
	t.Fatal("API server did not start in time")
}
