{"mappings": [{"request": {"method": "GET", "urlPathPattern": "/api/v1/templates/([^/]+)/([^/]+)"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"template_id": "{{request.pathSegments.[3]}}", "connection_id": "{{request.pathSegments.[2]}}", "is_default": true, "body": "Receipt template body from Wiremock", "header_text": {"font": "<PERSON><PERSON>", "style": "Bold", "color": "#000000"}, "footer_text": {"font": "<PERSON><PERSON>", "style": "Italic", "color": "#333333"}, "header_icon": {"src": "header.png", "width": 100, "height": 50}, "footer_icon": {"src": "footer.png", "width": 80, "height": 40}}}}, {"request": {"method": "POST", "urlPathPattern": "/api/v1/templates/([^/]+)"}, "response": {"status": 201, "headers": {"Content-Type": "application/json"}, "jsonBody": {"template_id": "{{jsonPath request.body '$.template_id'}}", "connection_id": "{{jsonPath request.body '$.connection_id'}}", "is_default": "{{jsonPath request.body '$.is_default'}}", "body": "{{jsonPath request.body '$.body'}}"}}}, {"request": {"method": "PUT", "urlPathPattern": "/api/v1/templates/([^/]+)/([^/]+)"}, "response": {"status": 204}}, {"request": {"method": "DELETE", "urlPathPattern": "/api/v1/templates/([^/]+)/([^/]+)"}, "response": {"status": 204}}, {"request": {"method": "GET", "url": "/health"}, "response": {"status": 200, "body": "OK"}}]}