version: '3.8'

services:
  # Mock API for template service
  mock-template-api:
    image: golang:1.24
    ports:
      - "8087:8080"
    volumes:
      - ./mock-api:/app
    working_dir: /app
    command: sh -c "go run server.go"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 5s

  # MongoDB for data storage
  mongodb:
    image: mongo:6.0.10
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
      MONGO_INITDB_DATABASE: mydb
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 5s

  # Wiremock for mocking external services
  wiremock:
    image: wiremock/wiremock:latest
    ports:
      - "8086:8080"
    volumes:
      - ./wiremock:/home/<USER>
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/__admin"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 5s

  # The service under test
  receipt-template-bff:
    build:
      context: ../..
      dockerfile: Dockerfile
    ports:
      - "8085:8080"
    environment:
      - LOG_LEVEL=debug
      - SERVER_PORT=8080
      - TEMPLATE_API_URL=http://mock-template-api:8080
      - HTTP_CLIENT_TIMEOUT=5s
      - HTTP_CLIENT_RETRY_MAX=3
    depends_on:
      mock-template-api:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      wiremock:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 5s