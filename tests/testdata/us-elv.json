{"country_code": "US", "party_code": "ELV", "cpo_url": "http://example.com/ocpi/cpo/elv", "endpoint": {"2.2.1": {"version": "2.2.1", "endpoints": [{"identifier": "cdrs", "url": "http://example.com/ocpi/cpo/2.2.1/cdrs", "role": "SENDER"}, {"identifier": "commands", "url": "http://example.com/ocpi/cpo/2.2.1/commands", "role": "RECEIVER"}, {"identifier": "credentials", "url": "http://example.com/ocpi/2.2.1/credentials", "role": "SENDER"}, {"identifier": "locations", "url": "http://example.com/ocpi/cpo/2.2.1/locations", "role": "SENDER"}, {"identifier": "sessions", "url": "http://example.com/ocpi/cpo/2.2.1/sessions", "role": "SENDER"}, {"identifier": "tariffs", "url": "http://example.com/ocpi/cpo/2.2.1/tariffs", "role": "SENDER"}, {"identifier": "tokens", "url": "http://example.com/ocpi/cpo/2.2.1/tokens", "role": "RECEIVER"}, {"identifier": "chargingprofiles", "url": "http://example.com/ocpi/cpo/2.2.1/chargingprofiles", "role": "RECEIVER"}, {"identifier": "hubclientinfo", "url": "http://example.com/ocpi/cpo/2.2.1/hubclientinfo", "role": "RECEIVER"}]}}, "credential": {"emsp": {"token": "emsp_token", "url": "https://elvis.hub-dev.invenco.com/ocpi/us/elv/emsp/versions", "roles": [{"role": "EMSP", "party_id": "ELV", "country_code": "US", "business_details": {"name": "elvis (us/elv)", "logo": {"category": "OPERATOR", "height": 512, "thumbnail": "https://www.invenco.com/themes/custom/invenco/logo.svg", "type": "svg", "url": "https://www.invenco.com/themes/custom/invenco/logo.svg", "width": 512}, "website": "https://elvis.hub-dev.invenco.com"}}]}, "cpo": {"token": "cpo_token", "url": "http://example.com/ocpi/versions", "roles": [{"role": "CPO", "party_id": "GVR", "country_code": "US", "business_details": {"name": "Gilbarco US Test", "logo": {}}}, {"role": "EMSP", "party_id": "GVR", "country_code": "US", "business_details": {"name": "Gilbarco US Test", "logo": {}}}]}}}