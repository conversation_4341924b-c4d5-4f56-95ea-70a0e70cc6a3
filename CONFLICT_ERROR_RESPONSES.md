# Template Conflict Error Responses (409)

This document describes the 409 Conflict error response format when template operations fail due to conflicts in the EV Receipt Template BFF service.

## Overview

When creating or updating templates via POST and PUT endpoints, the service may encounter conflicts from the external template API. If the external API returns a 409 status code, the BFF service will propagate this as a structured error response to the client.

## Affected Endpoints

- `POST /api/v1/templates/{connection_id}` - Create new template
- `PUT /api/v1/templates/{connection_id}/{template_id}` - Update existing template

## When 409 Conflicts Occur

409 Conflict responses are returned when:
- A template with the same connection information already exists
- The external template API detects a duplicate or conflicting template
- There are concurrent modifications to the same template resource

## Response Format

### HTTP Status Code
- **409 Conflict** - When template conflicts are detected

### Response Body Structure

**For POST requests:**
```json
{
  "status_message": "connection already exists"
}
```

**For PUT requests:**
The response body is empty (HTTP 409 with no content).

## Example Error Responses

### 1. POST Template Conflict

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US",
    "party_code": "DOM",
    "cpo_url": "https://example.com/cpo"
  }
}
```

**Response:**
```json
HTTP/1.1 409 Conflict
Content-Type: application/json

{
  "status_message": "connection already exists"
}
```

### 2. PUT Template Conflict

**Request:**
```json
PUT /api/v1/templates/550e8400-e29b-41d4-a716-446655440000/11111111-1111-1111-1111-111111111111
Content-Type: application/json

{
  "body": "Updated template body",
  "connection": {
    "country_code": "US",
    "party_code": "DOM",
    "cpo_url": "https://example.com/cpo"
  }
}
```

**Response:**
```json
HTTP/1.1 409 Conflict
```

## Error Handling Flow

1. **Client Request**: Client sends POST/PUT request to create/update template
2. **EMSP Validation**: Service validates EMSP connection (returns 400 if invalid)
3. **External API Call**: Service calls external template API
4. **Conflict Detection**: External API returns 409 if conflict exists
5. **Error Propagation**: Service returns 409 to client with appropriate message

## Client Error Handling

Clients should handle 409 responses by:

1. **Detecting the conflict**: Check for HTTP status code 409
2. **Informing the user**: Display appropriate error message about existing template
3. **Offering alternatives**: Suggest updating existing template or using different connection details
4. **Retry logic**: Implement appropriate retry mechanisms if needed

### Example Client Code

```javascript
try {
  const response = await fetch('/api/v1/templates/connection-id', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(templateData)
  });

  if (response.status === 409) {
    const errorData = await response.json();
    console.error('Template Conflict:', errorData.status_message);
    // Show user-friendly error message
    showError('A template with this connection already exists. Please use a different connection or update the existing template.');
  } else if (response.status === 400) {
    const errorData = await response.json();
    console.error('Validation Error:', errorData.status_message);
    showError('Invalid template data. Please check your input.');
  } else if (!response.ok) {
    console.error('Server Error:', response.status);
    showError('An unexpected error occurred. Please try again.');
  } else {
    console.log('Template created successfully');
  }
} catch (error) {
  console.error('Network Error:', error);
  showError('Network error. Please check your connection.');
}
```

## Relationship with Other Errors

- **400 Bad Request**: EMSP connection validation errors (handled before conflict check)
- **409 Conflict**: Template already exists (handled by external API)
- **500 Internal Server Error**: System errors (fallback for unexpected issues)

## Testing

To test 409 conflict handling:

1. Create a template with specific connection details
2. Attempt to create another template with the same connection details
3. Verify that the second request returns 409 Conflict
4. Test both POST and PUT endpoints for conflict scenarios

## Notes

- The conflict detection is handled by the external template API
- The BFF service acts as a proxy, propagating 409 responses from the external API
- POST requests return a JSON error message, while PUT requests return an empty body
- This follows the OpenAPI specification defined in `api-v1.yaml`
