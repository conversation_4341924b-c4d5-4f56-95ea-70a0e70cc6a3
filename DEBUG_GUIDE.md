# Debugging Guide for fe-ev-rcpt-tmpl-bff

This guide explains how to use the debugger setup for this Go application.

## Prerequisites

- VS Code with the Go extension installed
- Delve debugger (already installed at `/home/<USER>/go/bin/dlv`)
- Docker and Docker Compose (for integration tests and dependencies)

## Debug Configurations

The following debug configurations are available in VS Code:

### 1. Launch Main Application
- **Name**: "Launch Main Application"
- **Purpose**: Debug the main application with Docker dependencies
- **Pre-launch**: Starts Docker Compose services
- **Usage**: Use this for debugging the main application flow

### 2. Launch Main Application (No Docker)
- **Name**: "Launch Main Application (No Docker)"
- **Purpose**: Debug the main application without starting Docker services
- **Usage**: Use when Docker services are already running or not needed

### 3. Debug Tests
- **Name**: "Debug Tests"
- **Purpose**: Debug all tests in the project
- **Usage**: Run and debug all unit tests

### 4. Debug Current Test File
- **Name**: "Debug Current Test File"
- **Purpose**: Debug tests in the currently open test file
- **Usage**: Open a test file and use this configuration to debug only that file

### 5. Debug Integration Tests
- **Name**: "Debug Integration Tests"
- **Purpose**: Debug integration tests with Docker dependencies
- **Pre-launch**: Starts Docker Compose services
- **Usage**: Debug tests that require MongoDB or other external services

### 6. Attach to Process
- **Name**: "Attach to Process"
- **Purpose**: Attach debugger to a running Go process
- **Usage**: Debug a process that's already running

## How to Use

### Setting Breakpoints
1. Open the Go file you want to debug
2. Click in the gutter (left margin) next to the line number where you want to set a breakpoint
3. A red dot will appear indicating the breakpoint is set

### Starting Debug Session
1. Open the Debug panel (Ctrl+Shift+D or Cmd+Shift+D)
2. Select the appropriate debug configuration from the dropdown
3. Click the green play button or press F5

### Debug Controls
- **Continue (F5)**: Continue execution until the next breakpoint
- **Step Over (F10)**: Execute the current line and move to the next line
- **Step Into (F11)**: Step into function calls
- **Step Out (Shift+F11)**: Step out of the current function
- **Restart (Ctrl+Shift+F5)**: Restart the debug session
- **Stop (Shift+F5)**: Stop the debug session

### Inspecting Variables
- **Variables Panel**: Shows local variables, function parameters, and their values
- **Watch Panel**: Add expressions to watch their values during debugging
- **Call Stack**: Shows the current execution stack
- **Debug Console**: Execute Go expressions and commands

## Available Tasks

You can run these tasks from the Command Palette (Ctrl+Shift+P) by typing "Tasks: Run Task":

- **run-docker-compose**: Start Docker Compose services
- **stop-docker-compose**: Stop Docker Compose services
- **build**: Build the Go application
- **test**: Run all tests
- **test-integration**: Run integration tests with Docker dependencies
- **clean**: Clean Go build cache
- **mod-tidy**: Tidy Go modules

## Environment Variables

The debug configurations set the following environment variables:
- `GO_ENV=development` for main application debugging
- `GO_ENV=test` for test debugging

## Tips

1. **Use conditional breakpoints**: Right-click on a breakpoint to add conditions
2. **Log points**: Instead of adding print statements, use log points to output values
3. **Debug console**: Use the debug console to evaluate expressions during debugging
4. **Hot reload**: The debugger supports hot reload for some changes
5. **Memory inspection**: Use the Variables panel to inspect complex data structures

## Troubleshooting

### Common Issues

1. **Breakpoints not hit**: 
   - Ensure the code is compiled with debug symbols
   - Check that the breakpoint is on an executable line
   - Verify the correct debug configuration is selected

2. **Docker services not starting**:
   - Check if Docker is running
   - Verify docker-compose.yaml exists in the example directory
   - Check for port conflicts

3. **Tests failing in debug mode**:
   - Ensure test dependencies are available
   - Check environment variables are set correctly
   - Verify database connections for integration tests

### Debug Logs
Enable debug logs by setting `"showLog": true` in the debug configuration (already enabled).

## Advanced Debugging

### Remote Debugging
To debug a remote process:
1. Start your application with Delve in headless mode:
   ```bash
   dlv debug --headless --listen=:2345 --api-version=2 --accept-multiclient
   ```
2. Use the "Attach to Process" configuration and modify the port if needed

### Performance Debugging
For performance debugging, consider using:
- Go's built-in profiler (`go tool pprof`)
- Race condition detection (`go test -race`)
- Memory profiling with the debugger's memory inspection tools

## Configuration Files

The debugger setup includes these configuration files:
- `.vscode/launch.json`: Debug configurations
- `.vscode/tasks.json`: Build and test tasks
- `.vscode/settings.json`: Go-specific VS Code settings
