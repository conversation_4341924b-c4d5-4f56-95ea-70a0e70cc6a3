# EMSP Connection Validation Error Responses

This document describes the error response format when EMSP connection validation fails in the EV Receipt Template BFF service.

## Overview

When creating or updating templates via POST and PUT endpoints, the service validates that the EMSP connection exists in the MongoDB database. If validation fails, a structured error response is returned to the client.

## Affected Endpoints

- `POST /api/v1/templates/{connection_id}` - Create new template
- `PUT /api/v1/templates/{connection_id}/{template_id}` - Update existing template

## Validation Logic

The service validates EMSP connections and **all three fields are mandatory**:
- `country_code` - **Case-insensitive matching** (e.g., "US", "us", "Us" all match)
- `party_code` (mapped from `party_id` in the database) - **Case-insensitive matching** (e.g., "DOM", "dom", "Dom" all match)
- `cpo_url` - **Case-sensitive matching** (URLs are case-sensitive)

**All fields are required** - if any field is missing or empty, validation fails with a 400 error.

### Case-Insensitive Matching
- **Country Code**: "US" matches "us", "Us", "uS" in the database
- **Party ID**: "DOM" matches "dom", "Dom", "DoM" in the database
- **CPO URL**: Must match exactly (case-sensitive)

## Error Response Format

### HTTP Status Code
- **400 Bad Request** - When EMSP connection validation fails
- **500 Internal Server Error** - For other system errors

### Response Body Structure

```json
{
  "status_message": "EMSP connection not found for country_code: {country_code}, party_id: {party_id}, cpo_url: {cpo_url}",
  "status_code": 400,
  "timestamp": "2023-10-29T12:00:00Z"
}
```

**Note**: The `status_code` and `timestamp` fields are optional and may not always be present in the response.

## Example Error Responses

### 1. Invalid EMSP Connection

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US",
    "party_code": "INVALID",
    "cpo_url": "https://invalid-url.com"
  }
}
```

**Response:**
```json
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "status_message": "EMSP connection not found for country_code: US, party_id: INVALID, cpo_url: https://invalid-url.com"
}
```

### 2. Valid EMSP Connection

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US",
    "party_code": "DOM",
    "cpo_url": "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
  }
}
```

**Response:**
```json
HTTP/1.1 201 Created
```

### 3. Valid EMSP Connection with Different Case (Case-Insensitive)

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "us",
    "party_code": "dom",
    "cpo_url": "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
  }
}
```

**Response:**
```json
HTTP/1.1 201 Created
```

**Note**: This succeeds because country_code and party_code matching is case-insensitive. "us" matches "US" and "dom" matches "DOM" in the database.

### 4. Missing Connection Data (Validation Fails)

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body"
}
```

**Response:**
```json
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "status_message": "country_code is required"
}
```

### 5. Partial Connection Data (Validation Fails)

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US"
  }
}
```

**Response:**
```json
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "status_message": "party_id is required"
}
```

## Error Message Format

The error messages follow these patterns:

### Connection Not Found Error
```
EMSP connection not found for country_code: {country_code}, party_id: {party_id}, cpo_url: {cpo_url}
```

### Required Field Errors
```
country_code is required
party_id is required
cpo_url is required
```

Where:
- `{country_code}` - The country code from the request
- `{party_id}` - The party ID from the request
- `{cpo_url}` - The CPO URL from the request

## Client Error Handling

Clients should:

1. **Check HTTP Status Code**: Look for `400` status code to identify validation errors
2. **Parse Error Message**: Extract the `status_message` field from the JSON response
3. **Display User-Friendly Message**: Show appropriate error message to users
4. **Log Details**: Log the full error response for debugging

### Example Client Code (JavaScript)

```javascript
try {
  const response = await fetch('/api/v1/templates/connection-id', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(templateData)
  });

  if (response.status === 400) {
    const errorData = await response.json();
    console.error('EMSP Validation Error:', errorData.status_message);
    // Show user-friendly error message
    showError('Invalid EMSP connection. Please check your connection details.');
  } else if (!response.ok) {
    console.error('Server Error:', response.status);
    showError('An unexpected error occurred. Please try again.');
  } else {
    console.log('Template created successfully');
  }
} catch (error) {
  console.error('Network Error:', error);
  showError('Network error. Please check your connection.');
}
```

## Database Query

The validation performs the following MongoDB query with case-insensitive regex matching:
```javascript
db.connections.countDocuments({
  "country_code": { "$regex": "^US$", "$options": "i" },
  "party_code": { "$regex": "^DOM$", "$options": "i" },
  "cpo_url": "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
})
```

**Query Details:**
- `country_code` and `party_code` use case-insensitive regex matching (`$options: "i"`)
- `cpo_url` uses exact string matching (case-sensitive)
- Special regex characters in input are escaped using `regexp.QuoteMeta()`

If the count is 0, validation fails and the error response is returned.
